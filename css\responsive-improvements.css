/**
 * Responsive Improvements for Tendeal Theme
 * 
 * This file contains critical responsive fixes and improvements
 * for better mobile and tablet experience.
 */

/* ==========================================================================
   MOBILE-FIRST IMPROVEMENTS
   ========================================================================== */

/* Touch Target Improvements */
@media (max-width: 767.98px) {

  /* Minimum touch target size of 44px for mobile */
  .btn,
  .nav-link,
  .site-header__list li a,
  .mobile-nav-item a,
  .category-menu-button,
  .woocommerce .button,
  .woocommerce input[type="submit"] {
    min-height: 44px;
    min-width: 44px;
    padding: 0px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  /* Search Bar Mobile Optimization */
  .search-wrapper .aws-search-field {
    height: 44px !important;
    font-size: 16px !important;
    /* Prevents zoom on iOS */
    padding: 12px 16px !important;
  }

  .search-wrapper .aws-search-form {
    height: 44px !important;
  }

  .search-wrapper .aws-search-form .aws-form-btn {
    min-width: 44px;
    height: 44px;
  }

  /* Header Mobile Improvements */
  .site-header {
    padding: 8px 0;
  }

  .site-logo img {
    max-height: 40px;
  }

  /* Mobile Navigation Improvements */
  .main-navigation a {
    padding: 12px 16px !important;
    font-size: 16px;
    /* min-height: 24px; */
    display: flex;
    align-items: center;
  }

  /* Product Grid Mobile Optimization */
  .woocommerce ul.products li.product {
    width: 100% !important;
    margin-bottom: 20px;
  }

  .woocommerce ul.products li.product .woocommerce-loop-product__title {
    font-size: 16px;
    line-height: 1.4;
  }

  .woocommerce ul.products li.product .price {
    font-size: 18px;
    font-weight: 600;
  }

  /* Form Improvements */
  .form-control,
  .form-select,
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="tel"],
  textarea {
    min-height: 44px;
    font-size: 16px;
    /* Prevents zoom on iOS */
    padding: 12px 16px;
  }

  /* Button Improvements */
  .btn {
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
  }

  .btn-sm {
    min-height: 40px;
    padding: 10px 14px;
    font-size: 14px;
  }

  /* Cart and Checkout Improvements */
  .woocommerce-cart .shop_table {
    font-size: 14px;
  }

  .woocommerce-cart .shop_table td {
    padding: 12px 8px;
  }

  .woocommerce .quantity input.qty {
    min-height: 44px;
    min-width: 60px;
    text-align: center;
    font-size: 16px;
  }

  /* Mobile Bottom Navigation Improvements */
  .mobile-bottom-nav {
    padding: 12px 0;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  }

  .mobile-nav-item a {
    padding: 8px 4px;
    min-height: 60px;
    font-size: 12px;
  }

  .mobile-nav-item a i {
    font-size: 20px;
    margin-bottom: 4px;
  }

  /* Info Bar Mobile Optimization */
  .info-bar {
    padding: 8px 0;
  }

  .info-bar__list {
    gap: 8px;
    flex-wrap: wrap;
  }

  .info-bar__list li {
    font-size: 14px;
  }

  /* App Banner Mobile Optimization */
  .app-banner-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .app-banner-image img {
    max-height: 60px;
  }

  .app-banner-text h3 {
    font-size: 14px;
  }

  .btn-app-download {
    padding: 8px 16px;
    font-size: 14px;
  }
}

/* ==========================================================================
   TABLET IMPROVEMENTS (768px - 991px)
   ========================================================================== */

@media (min-width: 768px) and (max-width: 991.98px) {

  /* Tablet-specific optimizations */
  .site-header__list {
    gap: 8px;
  }

  .site-header__list li a {
    font-size: 12px;
    padding: 8px 10px;
  }

  /* Product grid for tablets */
  .woocommerce ul.products li.product {
    width: 48% !important;
    margin-right: 4%;
  }

  .woocommerce ul.products li.product:nth-child(2n) {
    margin-right: 0;
  }

  /* Category dropdown for tablets */
  .category-dropdown {
    width: 90vw;
    max-width: 600px;
  }
}

/* ==========================================================================
   IMPROVED RESPONSIVE UTILITIES
   ========================================================================== */

/* Better responsive spacing */
@media (max-width: 575.98px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .row {
    margin-left: -10px;
    margin-right: -10px;
  }

  .row>* {
    padding-left: 10px;
    padding-right: 10px;
  }
}

/* Responsive text sizing */
@media (max-width: 767.98px) {

  h1,
  .h1 {
    font-size: 1.75rem;
  }

  h2,
  .h2 {
    font-size: 1.5rem;
  }

  h3,
  .h3 {
    font-size: 1.25rem;
  }

  h4,
  .h4 {
    font-size: 1.125rem;
  }

  h5,
  .h5 {
    font-size: 1rem;
  }

  h6,
  .h6 {
    font-size: 0.875rem;
  }

  .lead {
    font-size: 1.125rem;
  }

  p {
    font-size: 14px;
    line-height: 1.5;
  }
}

/* Responsive images improvements */
.img-responsive,
.wp-post-image,
.woocommerce-product-gallery__image img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Better responsive tables */
@media (max-width: 767.98px) {
  .table-responsive {
    border: none;
  }

  .table-responsive table,
  .table-responsive thead,
  .table-responsive tbody,
  .table-responsive th,
  .table-responsive td,
  .table-responsive tr {
    display: block;
  }

  .table-responsive thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .table-responsive tr {
    border: 1px solid #ccc;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 8px;
  }

  .table-responsive td {
    border: none;
    position: relative;
    padding-left: 50% !important;
    text-align: right;
  }

  .table-responsive td:before {
    content: attr(data-label);
    position: absolute;
    left: 6px;
    width: 45%;
    text-align: left;
    font-weight: bold;
  }
}

/* ==========================================================================
   ACCESSIBILITY IMPROVEMENTS
   ========================================================================== */

/* Focus states for mobile */
@media (max-width: 767.98px) {

  .btn:focus,
  .form-control:focus,
  .nav-link:focus,
  a:focus {
    outline: 2px solid #ea9c00;
    outline-offset: 2px;
  }
}

/* Better contrast for mobile */
@media (max-width: 767.98px) {
  .text-muted {
    color: #666 !important;
  }

  .small,
  .text-small {
    font-size: 14px !important;
  }
}

/* ==========================================================================
   PERFORMANCE OPTIMIZATIONS
   ========================================================================== */

/* Reduce animations on mobile for better performance */
@media (max-width: 767.98px) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimize for touch devices */
@media (hover: none) and (pointer: coarse) {

  .btn:hover,
  .nav-link:hover,
  a:hover {
    transform: none;
    box-shadow: none;
  }
}

/* ==========================================================================
   CHECKOUT SHIPPING DISPLAY
   ========================================================================== */

/* Vendor shipping rows in checkout */
.shipping-vendor-row {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.shipping-vendor-row .total-label {
  font-size: 14px;
  color: #666;
  padding-left: 20px;
}

.shipping-vendor-row .total-value {
  font-size: 14px;
  color: #666;
}

/* Main shipping total row */
.shipping-row .total-label strong,
.shipping-row .total-value strong {
  color: #333;
  font-weight: 600;
}

/* Mobile responsive for checkout shipping */
@media (max-width: 767.98px) {
  .shipping-vendor-row {
    padding: 12px 0;
  }

  .shipping-vendor-row .total-label {
    font-size: 13px;
    padding-left: 15px;
  }

  .shipping-vendor-row .total-value {
    font-size: 13px;
  }
}