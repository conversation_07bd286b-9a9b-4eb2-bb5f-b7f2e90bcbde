/**
 * Optimized Cart Styles
 * Modern, responsive cart design with proper loading states and animations
 */

/* Cart Container */
.cart-container {
    --primary-color: #ea9c00;
    --secondary-color: #f8f9fa;
    --border-color: #dee2e6;
    --text-color: #333;
    --muted-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;

    position: relative;
    padding: 2rem 0;
    min-height: 60vh;
}

/* Cart Messages */
.cart-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 400px;
}

.cart-messages .alert {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 0.5rem;
}

.cart-messages .alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.cart-messages .alert-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.cart-messages .alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.cart-messages .btn-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.7;
}

.cart-messages .btn-close:hover {
    opacity: 1;
}

/* Loading Overlay */
.cart-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1040;
}

.loading-spinner {
    text-align: center;
}

.loading-spinner p {
    margin-top: 1rem;
    color: var(--muted-color);
    font-weight: 500;
}

/* Cart Header */
.cart-header {
    background: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
}

.checkbox-label {
    font-weight: 500;
    color: var(--text-color);
}

.cart-header-actions .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.cart-header-actions .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.selected-count {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Vendor Cart Section */
.vendor-cart-section {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.vendor-cart-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.vendor-header {
    background: var(--secondary-color);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.vendor-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.vendor-name {
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.vendor-item-count {
    color: var(--muted-color);
    font-size: 0.875rem;
}

.shipping-selection {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.shipping-selection-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.shipping-selection-btn:hover {
    border-color: var(--primary-color);
    background: #fef7e6;
}

.shipping-text {
    font-weight: 500;
}

.shipping-cost {
    color: var(--primary-color);
    font-weight: 600;
}

/* Product Items */
.vendor-products {
    padding: 0;
}

.cart-product-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
}

.cart-product-item:last-child {
    border-bottom: none;
}

.cart-product-item.updating {
    opacity: 0.6;
    pointer-events: none;
}

.cart-product-item.removing {
    background: #fff5f5;
    transform: scale(0.98);
}

.product-select {
    margin-right: 1rem;
}

.product-select input[type="checkbox"] {
    transform: scale(1.2);
}

.product-info {
    display: flex;
    flex: 1;
    gap: 1rem;
}

.product-thumbnail {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.product-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-details {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-weight: 600;
    color: var(--text-color);
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.product-name:hover {
    color: var(--primary-color);
}

.vendor-store-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--muted-color);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.product-reviews {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.star-rating {
    display: flex;
    gap: 2px;
}

.rating {
    font-weight: 500;
    color: var(--text-color);
}

.review-count,
.no-reviews {
    color: var(--muted-color);
}

/* Quantity Control */
.quantity-control {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
}

.qty-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.qty-btn:hover {
    border-color: var(--primary-color);
    background: #fef7e6;
}

.qty-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.qty-input {
    width: 60px;
    height: 32px;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-weight: 500;
}

.qty-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(234, 156, 0, 0.2);
}

.product-actions {
    margin-left: 1rem;
}

.remove-item-btn {
    width: 100% !important;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none !important;
    border: none;
    color: var(--danger-color) !important;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px !important;
    transition: all 0.2s ease;
}

.remove-item-btn:hover {
    background: #fff5f5;
    color: #c53030;
}

/* Product Price Section */
.product-price-section {
    min-width: 120px;
    text-align: right;
    margin-left: 1rem;
}

.product-subtotal {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Cart Sidebar */
.cart-sidebar {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.cart-address-box,
.cart-coupon,
.cart-summary {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow);
}

.cart-address-box h4,
.cart-coupon h4,
.cart-summary h4 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
}

/* Enhanced Coupon Section */
.cart-coupon input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.cart-coupon input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(234, 156, 0, 0.2);
}

.cart-coupon input.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.cart-coupon button {
    width: 100%;
    padding: 0.75rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cart-coupon button:hover:not(:disabled) {
    background: #d18700;
    transform: translateY(-1px);
}

.cart-coupon button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.cart-coupon button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.cart-coupon button:hover::before {
    left: 100%;
}

/* Applied Coupons */
.applied-coupons {
    margin-top: 1rem;
}

.applied-coupon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #c3e6cb;
    border-radius: var(--border-radius);
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    animation: slideInDown 0.3s ease;
}

.coupon-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.coupon-code {
    font-weight: 600;
    color: #155724;
    background: rgba(21, 87, 36, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
}

.coupon-discount {
    color: var(--success-color);
    font-weight: 600;
}

.remove-coupon {
    background: none;
    border: none;
    color: #721c24;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-coupon:hover {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    transform: scale(1.1);
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Cart Summary */
.cart-summary .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.cart-summary .summary-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.1rem;
    background: linear-gradient(135deg, #fef7e6 0%, #fff5e6 100%);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

.summary-row .amount {
    font-weight: 600;
    transition: all 0.3s ease;
}

.summary-row .amount.updating {
    opacity: 0.6;
    transform: scale(0.95);
}

.summary-row .amount.updated {
    color: var(--success-color);
    transform: scale(1.05);
}

.summary-row.discount-row {
    color: var(--success-color);
}

.summary-row.discount-row .amount {
    color: var(--success-color);
}

/* Vendor Totals */
.vendor-totals {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
}

.vendor-totals h6 {
    margin: 0 0 0.75rem 0;
    color: var(--text-color);
    font-weight: 600;
}

.vendor-total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.vendor-total-row:last-child {
    border-bottom: none;
    font-weight: 600;
    color: var(--primary-color);
}

.vendor-name-total {
    color: var(--text-color);
}

.vendor-amount {
    font-weight: 500;
}

.checkout-button {
    width: 100%;
    padding: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 1rem;
}

.checkout-button:hover {
    background: #d18700;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .cart-container {
        flex-direction: column;
    }

    .cart-sidebar {
        position: static;
        margin-top: 2rem;
    }
}

@media (max-width: 767.98px) {
    .cart-container {
        padding: 1rem 0;
    }

    .cart-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .cart-product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .product-info {
        width: 100%;
    }

    .product-price-section {
        width: 100%;
        text-align: left;
        margin-left: 0;
    }

    .quantity-control {
        justify-content: space-between;
        width: 100%;
    }

    .vendor-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 575.98px) {
    .cart-header-actions .btn .btn-text {
        display: none;
    }

    .product-thumbnail {
        width: 60px;
        height: 60px;
    }

    .cart-sidebar {
        padding: 1rem;
    }
}

/* Animation for updating states */
@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.6;
    }

    100% {
        opacity: 1;
    }
}

.cart-product-item.updating {
    animation: pulse 1.5s infinite;
}

/* Focus states for accessibility */
.qty-btn:focus,
.remove-item-btn:focus,
.shipping-selection-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .cart-container {
        --border-color: #000;
        --shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }

    .vendor-cart-section {
        border: 2px solid var(--border-color);
    }
}

/* Enhanced Vendor Grouping Styles */
.vendor-cart-section {
    position: relative;
    overflow: hidden;
}

.vendor-cart-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.vendor-cart-section:hover::before {
    opacity: 1;
}

.vendor-header {
    position: relative;
    background: linear-gradient(135deg, var(--secondary-color) 0%, #f1f3f4 100%);
}

.vendor-logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.vendor-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--muted-color);
}

.vendor-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.vendor-rating .star {
    color: var(--warning-color);
    font-size: 0.75rem;
}

/* Enhanced Product Item Styling */
.cart-product-item {
    position: relative;
    background: white;
    transition: all 0.3s ease;
}

.cart-product-item:hover {
    background: #fafbfc;
    transform: translateX(4px);
}

.cart-product-item::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.cart-product-item:hover::after {
    width: 3px;
}

.product-thumbnail {
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-thumbnail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.cart-product-item:hover .product-thumbnail::before {
    transform: translateX(100%);
}

.product-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--danger-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.sale {
    background: var(--danger-color);
}

.product-badge.new {
    background: var(--success-color);
}

.product-badge.limited {
    background: var(--warning-color);
    color: var(--text-color);
}

/* Enhanced Product Details */
.product-details {
    position: relative;
}

.product-name {
    position: relative;
    overflow: hidden;
}

.product-name::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.product-name:hover::after {
    width: 100%;
}

.product-attributes {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.product-attribute {
    background: var(--secondary-color);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    color: var(--muted-color);
}

.product-stock-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.stock-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.stock-indicator.in-stock {
    background: var(--success-color);
}

.stock-indicator.low-stock {
    background: var(--warning-color);
}

.stock-indicator.out-of-stock {
    background: var(--danger-color);
}

/* Enhanced Quantity Controls */
.quantity-control {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    position: relative;
}

.qty-btn {
    position: relative;
    overflow: hidden;
}

.qty-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(234, 156, 0, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.qty-btn:hover::before {
    width: 40px;
    height: 40px;
}

.qty-input {
    background: white;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.qty-input:focus {
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(234, 156, 0, 0.2);
}

/* Enhanced Price Display */
.product-price-section {
    background: #fef7e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    border-left: 4px solid var(--primary-color);
}

.price-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-label {
    font-size: 0.875rem;
    color: var(--muted-color);
}

.price-value {
    font-weight: 600;
    color: var(--text-color);
}

.price-value.total {
    font-size: 1.1rem;
    color: var(--primary-color);
}

.price-savings {
    color: var(--success-color);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Enhanced Remove Button */
.remove-item-btn {
    position: relative;
    overflow: hidden;
}

.remove-item-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.2), transparent);
    transition: left 0.5s ease;
}

.remove-item-btn:hover::before {
    left: 100%;
}

/* Enhanced Cart Sidebar */
.cart-sidebar {
    position: relative;
}

.cart-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #f39c12);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.cart-summary {
    position: relative;
    overflow: hidden;
}

.cart-summary::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(234, 156, 0, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

.summary-row {
    position: relative;
    z-index: 1;
}

.summary-row.total {
    background: linear-gradient(135deg, #fef7e6 0%, #fff5e6 100%);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

/* Enhanced Checkout Button */
.checkout-button {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--primary-color) 0%, #f39c12 100%);
    box-shadow: 0 4px 15px rgba(234, 156, 0, 0.3);
}

.checkout-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.checkout-button:hover::before {
    left: 100%;
}

.checkout-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(234, 156, 0, 0.4);
}

/* Progress Indicator */
.cart-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.progress-step::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 60%;
    width: 80%;
    height: 2px;
    background: var(--border-color);
    z-index: 1;
}

.progress-step:last-child::after {
    display: none;
}

.progress-step.active::after {
    background: var(--primary-color);
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.progress-step.active .step-icon {
    background: var(--primary-color);
}

.progress-step.completed .step-icon {
    background: var(--success-color);
}

.step-label {
    font-size: 0.875rem;
    color: var(--muted-color);
    text-align: center;
}

.progress-step.active .step-label {
    color: var(--text-color);
    font-weight: 500;
}

/* Empty Cart State */
.empty-cart {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.empty-cart-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--muted-color);
    font-size: 2rem;
}

.empty-cart h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.empty-cart p {
    color: var(--muted-color);
    margin-bottom: 2rem;
}

.continue-shopping-btn {
    background: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.continue-shopping-btn:hover {
    background: #d18700;
    transform: translateY(-2px);
    color: white;
}

/* Shipping Method Selection Modal */
.shipping-method-option {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
}

.shipping-method-option:hover {
    border-color: var(--primary-color);
    background: #fef7e6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(234, 156, 0, 0.2);
}

.shipping-method-option.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #fef7e6 0%, #fff5e6 100%);
    box-shadow: 0 4px 12px rgba(234, 156, 0, 0.3);
}

.method-radio {
    position: relative;
}

.method-radio input[type="radio"] {
    width: 20px;
    height: 20px;
    margin: 0;
    opacity: 0;
    cursor: pointer;
}

.method-radio label {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.method-radio label::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.3s ease;
}

.method-radio input[type="radio"]:checked+label {
    border-color: var(--primary-color);
}

.method-radio input[type="radio"]:checked+label::after {
    transform: translate(-50%, -50%) scale(1);
}

.method-info {
    flex: 1;
}

.method-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.method-name {
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.method-cost {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.method-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.delivery-time {
    display: flex;
    align-items: center;
    color: var(--muted-color);
    font-size: 0.875rem;
}

.method-description {
    color: var(--muted-color);
    font-size: 0.875rem;
    line-height: 1.4;
}

.method-icon {
    color: var(--primary-color);
    opacity: 0.7;
}

.shipping-method-option.selected .method-icon {
    opacity: 1;
}

/* Vendor Shipping Info in Modal */
.vendor-shipping-info {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.vendor-shipping-info .vendor-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.5rem;
}

/* Delivery Estimate */
.delivery-estimate .alert {
    border-left: 4px solid var(--primary-color);
    background: #fef7e6;
    border-color: transparent;
}

/* Modal Enhancements */
.modal-header {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #f1f3f4 100%);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    display: flex;
    align-items: center;
    color: var(--text-color);
}

.modal-footer {
    background: var(--secondary-color);
    border-top: 1px solid var(--border-color);
}

#confirm-shipping-method {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transition: all 0.3s ease;
}

#confirm-shipping-method:hover:not(:disabled) {
    background: #d18700;
    border-color: #d18700;
    transform: translateY(-1px);
}

#confirm-shipping-method:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Shipping Button Updates */
.shipping-selection-btn.updated {
    animation: shippingUpdated 2s ease;
}

@keyframes shippingUpdated {
    0% {
        background: #fef7e6;
    }

    50% {
        background: var(--success-color);
        color: white;
    }

    100% {
        background: #fef7e6;
    }
}

/* Mobile Responsive for Modal */
@media (max-width: 767.98px) {
    .modal-dialog {
        margin: 1rem;
    }

    .shipping-method-option {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .method-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .method-details {
        text-align: center;
    }

    .vendor-shipping-info .d-flex {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}

/* Address Management Styles */
.address-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-select-address,
.btn-update-address {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background: white;
    color: var(--text-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-select-address:hover,
.btn-update-address:hover {
    border-color: var(--primary-color);
    background: #fef7e6;
    color: var(--text-color);
}

/* Saved Address Modal */
.saved-address-option {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background: white;
}

.saved-address-option:hover {
    border-color: var(--primary-color);
    background: #fef7e6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(234, 156, 0, 0.2);
}

.saved-address-option.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #fef7e6 0%, #fff5e6 100%);
    box-shadow: 0 4px 12px rgba(234, 156, 0, 0.3);
}

.address-radio {
    position: relative;
    margin-top: 0.25rem;
}

.address-radio input[type="radio"] {
    width: 20px;
    height: 20px;
    margin: 0;
    opacity: 0;
    cursor: pointer;
}

.address-radio label {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.address-radio label::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.3s ease;
}

.address-radio input[type="radio"]:checked+label {
    border-color: var(--primary-color);
}

.address-radio input[type="radio"]:checked+label::after {
    transform: translate(-50%, -50%) scale(1);
}

.address-info {
    flex: 1;
}

.address-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.address-title {
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    font-size: 1rem;
}

.default-badge {
    background: var(--success-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.address-details {
    color: var(--muted-color);
    font-size: 0.875rem;
    line-height: 1.4;
}

.address-text {
    margin-bottom: 0.5rem;
}

.address-phone {
    display: flex;
    align-items: center;
    color: var(--text-color);
    font-weight: 500;
}

.address-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.edit-address {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.edit-address:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* Address Form Enhancements */
.address-update-form {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1rem;
}

.address-update-form .form-group {
    margin-bottom: 1rem;
}

.address-update-form .form-row {
    display: flex;
    gap: 1rem;
}

.address-update-form .form-group.half {
    flex: 1;
}

.address-update-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.address-update-form input,
.address-update-form select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.address-update-form input:focus,
.address-update-form select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(234, 156, 0, 0.2);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.btn-cancel,
.btn-save {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: var(--muted-color);
    color: white;
}

.btn-cancel:hover {
    background: #5a6268;
}

.btn-save {
    background: var(--primary-color);
    color: white;
}

.btn-save:hover {
    background: #d18700;
    transform: translateY(-1px);
}

/* Mobile Responsive for Address Modal */
@media (max-width: 767.98px) {
    .address-actions {
        flex-direction: column;
    }

    .saved-address-option {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .address-header {
        justify-content: center;
    }

    .address-details {
        text-align: center;
    }

    .address-update-form .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-actions {
        flex-direction: column;
    }
}