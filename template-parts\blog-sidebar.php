<?php
/**
 * Template part for displaying blog sidebar
 *
 * @package tendeal
 */
?>

<!-- Search Widget -->
<div class="sidebar-widget bg-white rounded p-4 mb-4">
    <h5 class="widget-title mb-3">Search Blog</h5>
    <form role="search" method="get" action="<?php echo home_url('/'); ?>">
        <div class="input-group">
            <input type="search" class="form-control" placeholder="Search posts..." value="<?php echo get_search_query(); ?>" name="s">
            <button class="btn btn-primary" type="submit">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </form>
</div>

<!-- Recent Posts Widget -->
<div class="sidebar-widget bg-white rounded p-4 mb-4">
    <h5 class="widget-title mb-3">Recent Posts</h5>
    <?php
    $recent_posts = get_posts(array(
        'numberposts' => 5,
        'post_status' => 'publish'
    ));
    
    if ($recent_posts) :
    ?>
        <div class="recent-posts-list">
            <?php foreach ($recent_posts as $post) : setup_postdata($post); ?>
                <div class="recent-post-item d-flex mb-3 pb-3 border-bottom">
                    <div class="recent-post-image me-3">
                        <?php if (has_post_thumbnail($post->ID)) : ?>
                            <a href="<?php echo get_permalink($post->ID); ?>">
                                <?php echo get_the_post_thumbnail($post->ID, 'thumbnail', array('class' => 'img-fluid rounded', 'style' => 'width: 60px; height: 60px; object-fit: cover;')); ?>
                            </a>
                        <?php else : ?>
                            <div class="placeholder-image bg-light rounded d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="recent-post-content flex-grow-1">
                        <h6 class="mb-1">
                            <a href="<?php echo get_permalink($post->ID); ?>" class="text-decoration-none text-dark">
                                <?php echo wp_trim_words(get_the_title($post->ID), 6, '...'); ?>
                            </a>
                        </h6>
                        <small class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <?php echo get_the_date('M j, Y', $post->ID); ?>
                        </small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php wp_reset_postdata(); ?>
    <?php endif; ?>
</div>

<!-- Categories Widget -->
<div class="sidebar-widget bg-white rounded p-4 mb-4">
    <h5 class="widget-title mb-3">Categories</h5>
    <?php
    $categories = get_categories(array(
        'orderby' => 'count',
        'order' => 'DESC',
        'number' => 10
    ));
    
    if ($categories) :
    ?>
        <div class="categories-list">
            <?php foreach ($categories as $category) : ?>
                <div class="category-item d-flex justify-content-between align-items-center mb-2">
                    <a href="<?php echo get_category_link($category->term_id); ?>" class="text-decoration-none text-dark">
                        <i class="fas fa-folder me-2 text-primary"></i>
                        <?php echo $category->name; ?>
                    </a>
                    <span class="badge bg-light text-dark"><?php echo $category->count; ?></span>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Popular Tags Widget -->
<div class="sidebar-widget bg-white rounded p-4 mb-4">
    <h5 class="widget-title mb-3">Popular Tags</h5>
    <?php
    $tags = get_tags(array(
        'orderby' => 'count',
        'order' => 'DESC',
        'number' => 15
    ));
    
    if ($tags) :
    ?>
        <div class="tags-cloud">
            <?php foreach ($tags as $tag) : ?>
                <a href="<?php echo get_tag_link($tag->term_id); ?>" class="badge bg-light text-dark text-decoration-none me-2 mb-2">
                    <?php echo $tag->name; ?> (<?php echo $tag->count; ?>)
                </a>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Newsletter Widget -->
<div class="sidebar-widget bg-primary text-white rounded p-4 mb-4">
    <h5 class="widget-title mb-3 text-white">Newsletter</h5>
    <p class="mb-3">Subscribe to our newsletter to get the latest updates and offers.</p>
    <form class="newsletter-form">
        <div class="mb-3">
            <input type="email" class="form-control" placeholder="Your email address" required>
        </div>
        <button type="submit" class="btn btn-light w-100">
            <i class="fas fa-paper-plane me-2"></i>
            Subscribe
        </button>
    </form>
</div>

<!-- Archive Widget -->
<div class="sidebar-widget bg-white rounded p-4 mb-4">
    <h5 class="widget-title mb-3">Archives</h5>
    <?php
    $archives = wp_get_archives(array(
        'type' => 'monthly',
        'limit' => 12,
        'format' => 'custom',
        'echo' => false
    ));
    
    if ($archives) :
        // Parse the archives output to create a custom format
        $archive_links = explode('</li>', $archives);
        foreach ($archive_links as $link) :
            if (trim($link)) :
                // Extract the URL and text from the link
                preg_match('/<a href="([^"]*)"[^>]*>([^<]*)<\/a>/', $link, $matches);
                if (isset($matches[1]) && isset($matches[2])) :
    ?>
                    <div class="archive-item d-flex justify-content-between align-items-center mb-2">
                        <a href="<?php echo $matches[1]; ?>" class="text-decoration-none text-dark">
                            <i class="fas fa-archive me-2 text-primary"></i>
                            <?php echo $matches[2]; ?>
                        </a>
                    </div>
    <?php
                endif;
            endif;
        endforeach;
    endif;
    ?>
</div>

<!-- Social Media Widget -->
<div class="sidebar-widget bg-white rounded p-4 mb-4">
    <h5 class="widget-title mb-3">Follow Us</h5>
    <div class="social-links">
        <div class="row g-2">
            <div class="col-6">
                <a href="#" class="btn btn-outline-primary w-100 btn-sm">
                    <i class="fab fa-facebook-f"></i> Facebook
                </a>
            </div>
            <div class="col-6">
                <a href="#" class="btn btn-outline-info w-100 btn-sm">
                    <i class="fab fa-twitter"></i> Twitter
                </a>
            </div>
            <div class="col-6">
                <a href="#" class="btn btn-outline-danger w-100 btn-sm">
                    <i class="fab fa-instagram"></i> Instagram
                </a>
            </div>
            <div class="col-6">
                <a href="#" class="btn btn-outline-primary w-100 btn-sm">
                    <i class="fab fa-linkedin-in"></i> LinkedIn
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Ad Space Widget -->
<div class="sidebar-widget bg-light rounded p-4 mb-4 text-center">
    <h6 class="widget-title mb-3">Advertisement</h6>
    <div class="ad-placeholder bg-white border rounded p-4">
        <i class="fas fa-ad fa-2x text-muted mb-2"></i>
        <p class="text-muted mb-0">Your Ad Here</p>
        <small class="text-muted">300x250</small>
    </div>
</div>
