<?php
/**
 * The template for displaying blog search results
 *
 * @package tendeal
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container mt-4">
        
        <!-- Search Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="search-header bg-white p-4 rounded">
                    <h1 class="search-title mb-3">
                        <i class="fas fa-search me-2 text-primary"></i>
                        <?php
                        printf(
                            esc_html__('Search Results for: %s', 'tendeal'),
                            '<span class="text-primary">"' . get_search_query() . '"</span>'
                        );
                        ?>
                    </h1>
                    
                    <?php if (have_posts()) : ?>
                        <div class="search-stats">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <?php
                                global $wp_query;
                                $total_results = $wp_query->found_posts;
                                printf(
                                    _n('%s result found', '%s results found', $total_results, 'tendeal'),
                                    number_format_i18n($total_results)
                                );
                                ?>
                            </small>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Search Form -->
                    <div class="search-form-wrapper mt-3 pt-3 border-top">
                        <form role="search" method="get" action="<?php echo home_url('/'); ?>" class="search-form">
                            <div class="input-group">
                                <input type="search" class="form-control" placeholder="Search blog posts..." value="<?php echo get_search_query(); ?>" name="s" required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Breadcrumb -->
        <div class="row mb-3">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-white p-3 rounded">
                        <li class="breadcrumb-item">
                            <a href="<?php echo home_url(); ?>" class="text-decoration-none">
                                <i class="fas fa-home"></i> Home
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo get_permalink(get_page_by_path('blogs')); ?>" class="text-decoration-none">Blog</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            Search Results
                        </li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <?php if (have_posts()) : ?>
                    
                    <!-- Search Results -->
                    <div class="search-results">
                        <?php while (have_posts()) : the_post(); ?>
                            <article id="post-<?php the_ID(); ?>" <?php post_class('search-result-item bg-white rounded p-4 mb-4 shadow-sm'); ?>>
                                
                                <div class="row">
                                    <!-- Post Image -->
                                    <div class="col-md-3">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('medium', array('class' => 'img-fluid rounded w-100', 'style' => 'height: 150px; object-fit: cover;')); ?>
                                            </a>
                                        <?php else : ?>
                                            <div class="search-image-placeholder bg-light d-flex align-items-center justify-content-center rounded" style="height: 150px;">
                                                <i class="fas fa-image fa-2x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Post Content -->
                                    <div class="col-md-9">
                                        
                                        <!-- Post Meta -->
                                        <div class="search-meta mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar-alt me-1"></i>
                                                <?php echo get_the_date('M j, Y'); ?>
                                            </small>
                                            <?php
                                            $categories = get_the_category();
                                            if (!empty($categories)) :
                                            ?>
                                                <small class="text-muted ms-3">
                                                    <i class="fas fa-tag me-1"></i>
                                                    <a href="<?php echo get_category_link($categories[0]->term_id); ?>" class="text-decoration-none text-muted">
                                                        <?php echo $categories[0]->name; ?>
                                                    </a>
                                                </small>
                                            <?php endif; ?>
                                            <small class="text-muted ms-3">
                                                <i class="fas fa-user me-1"></i>
                                                <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>" class="text-decoration-none text-muted">
                                                    <?php echo get_the_author(); ?>
                                                </a>
                                            </small>
                                        </div>
                                        
                                        <!-- Post Title -->
                                        <h3 class="search-title h4 mb-2">
                                            <a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
                                                <?php the_title(); ?>
                                            </a>
                                        </h3>
                                        
                                        <!-- Post Excerpt -->
                                        <div class="search-excerpt text-muted mb-3">
                                            <?php
                                            $excerpt = get_the_excerpt();
                                            $search_query = get_search_query();
                                            
                                            // Highlight search terms in excerpt
                                            if ($search_query) {
                                                $highlighted_excerpt = preg_replace(
                                                    '/(' . preg_quote($search_query, '/') . ')/i',
                                                    '<mark class="bg-warning">$1</mark>',
                                                    $excerpt
                                                );
                                                echo $highlighted_excerpt;
                                            } else {
                                                echo $excerpt;
                                            }
                                            ?>
                                        </div>
                                        
                                        <!-- Post Actions -->
                                        <div class="search-actions">
                                            <a href="<?php the_permalink(); ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-arrow-right me-1"></i>
                                                Read More
                                            </a>
                                            
                                            <?php if (comments_open() || get_comments_number()) : ?>
                                                <a href="<?php the_permalink(); ?>#comments" class="btn btn-outline-secondary btn-sm ms-2">
                                                    <i class="fas fa-comments me-1"></i>
                                                    <?php
                                                    $comment_count = get_comments_number();
                                                    if ($comment_count == 0) {
                                                        echo 'No Comments';
                                                    } elseif ($comment_count == 1) {
                                                        echo '1 Comment';
                                                    } else {
                                                        echo $comment_count . ' Comments';
                                                    }
                                                    ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        <?php endwhile; ?>
                    </div>

                    <!-- Pagination -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <nav aria-label="Search pagination">
                                <div class="pagination-wrapper d-flex justify-content-center">
                                    <?php
                                    echo paginate_links(array(
                                        'format' => '?paged=%#%',
                                        'show_all' => false,
                                        'end_size' => 1,
                                        'mid_size' => 2,
                                        'prev_next' => true,
                                        'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                                        'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
                                        'type' => 'list',
                                        'class' => 'pagination'
                                    ));
                                    ?>
                                </div>
                            </nav>
                        </div>
                    </div>

                <?php else : ?>
                    
                    <!-- No Results Found -->
                    <div class="no-results bg-white p-5 text-center rounded">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3>No results found</h3>
                        <p class="text-muted mb-4">
                            Sorry, no posts matched your search criteria. Please try again with different keywords.
                        </p>
                        
                        <!-- Search Suggestions -->
                        <div class="search-suggestions">
                            <h5 class="mb-3">Search Suggestions:</h5>
                            <ul class="list-unstyled text-start">
                                <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i>Try using different keywords</li>
                                <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i>Check your spelling</li>
                                <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i>Use more general terms</li>
                                <li class="mb-2"><i class="fas fa-lightbulb text-warning me-2"></i>Try searching for related topics</li>
                            </ul>
                        </div>
                        
                        <div class="mt-4">
                            <a href="<?php echo get_permalink(get_page_by_path('blogs')); ?>" class="btn btn-primary me-2">
                                <i class="fas fa-arrow-left me-2"></i>
                                Browse All Posts
                            </a>
                            <button type="button" class="btn btn-outline-primary" onclick="document.querySelector('input[name=s]').focus();">
                                <i class="fas fa-search me-2"></i>
                                Try Another Search
                            </button>
                        </div>
                    </div>

                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <?php get_template_part('template-parts/blog-sidebar'); ?>
            </div>
        </div>
    </div>
</main>

<?php
get_footer();
?>
