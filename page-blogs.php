<?php
/**
 * Template Name: Blogs Page
 * 
 * The template for displaying the blogs listing page
 *
 * @package tendeal
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <div class="page-header bg-white p-4 mb-4 rounded">
                    <h1 class="page-title text-center mb-3">Our Blog</h1>
                    <p class="text-center text-muted">Stay updated with the latest news, tips, and insights</p>
                </div>
            </div>
        </div>

        <!-- Featured Blog Post -->
        <?php
        $featured_posts = get_posts(array(
            'numberposts' => 1,
            'post_status' => 'publish',
            'meta_key' => 'featured_post',
            'meta_value' => '1'
        ));
        
        if (empty($featured_posts)) {
            $featured_posts = get_posts(array(
                'numberposts' => 1,
                'post_status' => 'publish'
            ));
        }
        
        if (!empty($featured_posts)) :
            $featured_post = $featured_posts[0];
            setup_postdata($featured_post);
        ?>
        <div class="row mb-5">
            <div class="col-12">
                <div class="featured-post bg-white rounded overflow-hidden shadow-sm">
                    <div class="row g-0">
                        <div class="col-md-6">
                            <?php if (has_post_thumbnail($featured_post->ID)) : ?>
                                <div class="featured-image h-100">
                                    <?php echo get_the_post_thumbnail($featured_post->ID, 'large', array('class' => 'img-fluid h-100 w-100', 'style' => 'object-fit: cover;')); ?>
                                </div>
                            <?php else : ?>
                                <div class="featured-image-placeholder bg-light d-flex align-items-center justify-content-center h-100">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <div class="card-body p-4 h-100 d-flex flex-column">
                                <div class="mb-2">
                                    <span class="badge bg-primary">Featured</span>
                                    <small class="text-muted ms-2"><?php echo get_the_date('M j, Y', $featured_post->ID); ?></small>
                                </div>
                                <h2 class="card-title h3 mb-3">
                                    <a href="<?php echo get_permalink($featured_post->ID); ?>" class="text-decoration-none text-dark">
                                        <?php echo get_the_title($featured_post->ID); ?>
                                    </a>
                                </h2>
                                <p class="card-text text-muted mb-3 flex-grow-1">
                                    <?php echo wp_trim_words(get_the_excerpt($featured_post->ID), 25, '...'); ?>
                                </p>
                                <div class="mt-auto">
                                    <a href="<?php echo get_permalink($featured_post->ID); ?>" class="btn btn-outline-primary">
                                        Read More <i class="fas fa-arrow-right ms-1"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
        wp_reset_postdata();
        endif;
        ?>

        <!-- Blog Categories Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="blog-categories bg-white p-3 rounded">
                    <div class="d-flex flex-wrap align-items-center">
                        <span class="me-3 fw-semibold">Categories:</span>
                        <a href="<?php echo get_permalink(); ?>" class="btn btn-sm btn-outline-secondary me-2 mb-2 category-filter active" data-category="all">All</a>
                        <?php
                        $categories = get_categories(array(
                            'orderby' => 'count',
                            'order' => 'DESC',
                            'number' => 8
                        ));
                        foreach ($categories as $category) :
                        ?>
                            <a href="<?php echo get_category_link($category->term_id); ?>" class="btn btn-sm btn-outline-secondary me-2 mb-2 category-filter" data-category="<?php echo $category->slug; ?>">
                                <?php echo $category->name; ?> (<?php echo $category->count; ?>)
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Blog Posts Grid -->
        <div class="row" id="blog-posts-container">
            <?php
            $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
            $blog_posts = new WP_Query(array(
                'post_type' => 'post',
                'posts_per_page' => 9,
                'paged' => $paged,
                'post_status' => 'publish',
                'post__not_in' => !empty($featured_posts) ? array($featured_posts[0]->ID) : array()
            ));

            if ($blog_posts->have_posts()) :
                while ($blog_posts->have_posts()) : $blog_posts->the_post();
            ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <article class="blog-card bg-white rounded overflow-hidden shadow-sm h-100">
                        <div class="blog-card-image">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium', array('class' => 'img-fluid w-100', 'style' => 'height: 200px; object-fit: cover;')); ?>
                                </a>
                            <?php else : ?>
                                <div class="blog-image-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-image fa-2x text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-body p-3 d-flex flex-column">
                            <div class="blog-meta mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    <?php echo get_the_date('M j, Y'); ?>
                                </small>
                                <?php
                                $categories = get_the_category();
                                if (!empty($categories)) :
                                ?>
                                    <small class="text-muted ms-2">
                                        <i class="fas fa-tag me-1"></i>
                                        <a href="<?php echo get_category_link($categories[0]->term_id); ?>" class="text-decoration-none text-muted">
                                            <?php echo $categories[0]->name; ?>
                                        </a>
                                    </small>
                                <?php endif; ?>
                            </div>
                            <h3 class="blog-title h5 mb-2">
                                <a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
                                    <?php the_title(); ?>
                                </a>
                            </h3>
                            <p class="blog-excerpt text-muted mb-3 flex-grow-1">
                                <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                            </p>
                            <div class="blog-footer mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        <?php echo get_the_author(); ?>
                                    </small>
                                    <a href="<?php the_permalink(); ?>" class="btn btn-sm btn-outline-primary">
                                        Read More
                                    </a>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>
            <?php
                endwhile;
            else :
            ?>
                <div class="col-12">
                    <div class="no-posts bg-white p-5 text-center rounded">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <h3>No blog posts found</h3>
                        <p class="text-muted">Check back later for new content!</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($blog_posts->max_num_pages > 1) : ?>
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Blog pagination">
                    <div class="pagination-wrapper d-flex justify-content-center">
                        <?php
                        echo paginate_links(array(
                            'total' => $blog_posts->max_num_pages,
                            'current' => $paged,
                            'format' => '?paged=%#%',
                            'show_all' => false,
                            'end_size' => 1,
                            'mid_size' => 2,
                            'prev_next' => true,
                            'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                            'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
                            'type' => 'list',
                            'class' => 'pagination'
                        ));
                        ?>
                    </div>
                </nav>
            </div>
        </div>
        <?php endif; ?>

        <?php wp_reset_postdata(); ?>
    </div>
</main>

<?php
get_footer();
?>
