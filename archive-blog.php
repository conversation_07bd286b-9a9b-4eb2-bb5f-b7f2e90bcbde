<?php
/**
 * The template for displaying blog archive pages (categories, tags, dates, authors)
 *
 * @package tendeal
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container mt-4">
        
        <!-- Archive Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="archive-header bg-white p-4 rounded">
                    <?php if (have_posts()) : ?>
                        <div class="archive-title-wrapper">
                            <?php
                            the_archive_title('<h1 class="archive-title mb-3">', '</h1>');
                            the_archive_description('<div class="archive-description text-muted">', '</div>');
                            ?>
                        </div>
                        
                        <!-- Archive Stats -->
                        <div class="archive-stats mt-3 pt-3 border-top">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-newspaper me-1"></i>
                                        <?php
                                        global $wp_query;
                                        $total_posts = $wp_query->found_posts;
                                        printf(
                                            _n('%s post found', '%s posts found', $total_posts, 'tendeal'),
                                            number_format_i18n($total_posts)
                                        );
                                        ?>
                                    </small>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <a href="<?php echo get_permalink(get_page_by_path('blogs')); ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Back to All Posts
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Breadcrumb -->
        <div class="row mb-3">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-white p-3 rounded">
                        <li class="breadcrumb-item">
                            <a href="<?php echo home_url(); ?>" class="text-decoration-none">
                                <i class="fas fa-home"></i> Home
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo get_permalink(get_page_by_path('blogs')); ?>" class="text-decoration-none">Blog</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">
                            <?php
                            if (is_category()) {
                                echo 'Category: ' . single_cat_title('', false);
                            } elseif (is_tag()) {
                                echo 'Tag: ' . single_tag_title('', false);
                            } elseif (is_author()) {
                                echo 'Author: ' . get_the_author();
                            } elseif (is_date()) {
                                if (is_year()) {
                                    echo 'Year: ' . get_the_date('Y');
                                } elseif (is_month()) {
                                    echo 'Month: ' . get_the_date('F Y');
                                } elseif (is_day()) {
                                    echo 'Day: ' . get_the_date('F j, Y');
                                }
                            } else {
                                echo 'Archives';
                            }
                            ?>
                        </li>
                    </ol>
                </nav>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <?php if (have_posts()) : ?>
                    
                    <!-- Posts Grid -->
                    <div class="row" id="archive-posts-container">
                        <?php while (have_posts()) : the_post(); ?>
                            <div class="col-md-6 mb-4">
                                <article id="post-<?php the_ID(); ?>" <?php post_class('blog-card bg-white rounded overflow-hidden shadow-sm h-100'); ?>>
                                    
                                    <!-- Post Image -->
                                    <div class="blog-card-image">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('medium', array('class' => 'img-fluid w-100', 'style' => 'height: 200px; object-fit: cover;')); ?>
                                            </a>
                                        <?php else : ?>
                                            <div class="blog-image-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                                <i class="fas fa-image fa-2x text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Post Content -->
                                    <div class="card-body p-3 d-flex flex-column">
                                        
                                        <!-- Post Meta -->
                                        <div class="blog-meta mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar-alt me-1"></i>
                                                <?php echo get_the_date('M j, Y'); ?>
                                            </small>
                                            <?php
                                            $categories = get_the_category();
                                            if (!empty($categories)) :
                                            ?>
                                                <small class="text-muted ms-2">
                                                    <i class="fas fa-tag me-1"></i>
                                                    <a href="<?php echo get_category_link($categories[0]->term_id); ?>" class="text-decoration-none text-muted">
                                                        <?php echo $categories[0]->name; ?>
                                                    </a>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <!-- Post Title -->
                                        <h3 class="blog-title h5 mb-2">
                                            <a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
                                                <?php the_title(); ?>
                                            </a>
                                        </h3>
                                        
                                        <!-- Post Excerpt -->
                                        <p class="blog-excerpt text-muted mb-3 flex-grow-1">
                                            <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                                        </p>
                                        
                                        <!-- Post Footer -->
                                        <div class="blog-footer mt-auto">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">
                                                    <i class="fas fa-user me-1"></i>
                                                    <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>" class="text-decoration-none text-muted">
                                                        <?php echo get_the_author(); ?>
                                                    </a>
                                                </small>
                                                <a href="<?php the_permalink(); ?>" class="btn btn-sm btn-outline-primary">
                                                    Read More
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        <?php endwhile; ?>
                    </div>

                    <!-- Pagination -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <nav aria-label="Archive pagination">
                                <div class="pagination-wrapper d-flex justify-content-center">
                                    <?php
                                    echo paginate_links(array(
                                        'format' => '?paged=%#%',
                                        'show_all' => false,
                                        'end_size' => 1,
                                        'mid_size' => 2,
                                        'prev_next' => true,
                                        'prev_text' => '<i class="fas fa-chevron-left"></i> Previous',
                                        'next_text' => 'Next <i class="fas fa-chevron-right"></i>',
                                        'type' => 'list',
                                        'class' => 'pagination'
                                    ));
                                    ?>
                                </div>
                            </nav>
                        </div>
                    </div>

                <?php else : ?>
                    
                    <!-- No Posts Found -->
                    <div class="no-posts bg-white p-5 text-center rounded">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3>No posts found</h3>
                        <p class="text-muted mb-4">
                            <?php
                            if (is_category()) {
                                echo 'No posts found in this category.';
                            } elseif (is_tag()) {
                                echo 'No posts found with this tag.';
                            } elseif (is_author()) {
                                echo 'This author has not published any posts yet.';
                            } elseif (is_date()) {
                                echo 'No posts found for this date.';
                            } else {
                                echo 'No posts found in this archive.';
                            }
                            ?>
                        </p>
                        <a href="<?php echo get_permalink(get_page_by_path('blogs')); ?>" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to All Posts
                        </a>
                    </div>

                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <?php get_template_part('template-parts/blog-sidebar'); ?>
            </div>
        </div>
    </div>
</main>

<?php
get_footer();
?>
