<?php
/**
 * The template for displaying comments for blog posts
 *
 * @package tendeal
 */

/*
 * If the current post is protected by a password and
 * the visitor has not yet entered the password we will
 * return early without loading the comments.
 */
if ( post_password_required() ) {
	return;
}
?>

<div id="comments" class="comments-area bg-white rounded p-4 mt-4">

	<?php
	// You can start editing here -- including this comment!
	if ( have_comments() ) :
		?>
		<h4 class="comments-title mb-4">
			<i class="fas fa-comments me-2 text-primary"></i>
			<?php
			$tendeal_comment_count = get_comments_number();
			if ( '1' === $tendeal_comment_count ) {
				printf(
					/* translators: 1: title. */
					esc_html__( 'One comment on &ldquo;%1$s&rdquo;', 'tendeal' ),
					'<span>' . wp_kses_post( get_the_title() ) . '</span>'
				);
			} else {
				printf(
					/* translators: 1: comment count number, 2: title. */
					esc_html( _nx( '%1$s comment on &ldquo;%2$s&rdquo;', '%1$s comments on &ldquo;%2$s&rdquo;', $tendeal_comment_count, 'comments title', 'tendeal' ) ),
					number_format_i18n( $tendeal_comment_count ), // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
					'<span>' . wp_kses_post( get_the_title() ) . '</span>'
				);
			}
			?>
		</h4><!-- .comments-title -->

		<?php the_comments_navigation(); ?>

		<ol class="comment-list list-unstyled">
			<?php
			wp_list_comments(
				array(
					'style'      => 'ol',
					'short_ping' => true,
					'callback'   => 'tendeal_blog_comment_callback',
				)
			);
			?>
		</ol><!-- .comment-list -->

		<?php
		the_comments_navigation();

		// If comments are closed and there are comments, let's leave a little note, shall we?
		if ( ! comments_open() ) :
			?>
			<p class="no-comments alert alert-info">
				<i class="fas fa-info-circle me-2"></i>
				<?php esc_html_e( 'Comments are closed.', 'tendeal' ); ?>
			</p>
			<?php
		endif;

	endif; // Check for have_comments().

	// Comment form
	$commenter = wp_get_current_commenter();
	$req = get_option( 'require_name_email' );
	$aria_req = ( $req ? " aria-required='true'" : '' );

	$comment_form_args = array(
		'title_reply'          => '<h4 class="mb-4"><i class="fas fa-edit me-2 text-primary"></i>' . __( 'Leave a Comment', 'tendeal' ) . '</h4>',
		'title_reply_to'       => '<h4 class="mb-4"><i class="fas fa-reply me-2 text-primary"></i>' . __( 'Reply to %s', 'tendeal' ) . '</h4>',
		'cancel_reply_link'    => __( 'Cancel Reply', 'tendeal' ),
		'label_submit'         => __( 'Post Comment', 'tendeal' ),
		'submit_button'        => '<button type="submit" class="btn btn-primary"><i class="fas fa-paper-plane me-2"></i>%4$s</button>',
		'comment_field'        => '<div class="mb-3">
									<label for="comment" class="form-label">' . _x( 'Comment', 'noun', 'tendeal' ) . ' <span class="text-danger">*</span></label>
									<textarea id="comment" name="comment" class="form-control" rows="6" placeholder="' . __( 'Write your comment here...', 'tendeal' ) . '" required></textarea>
								</div>',
		'fields'               => array(
			'author' => '<div class="row">
							<div class="col-md-6 mb-3">
								<label for="author" class="form-label">' . __( 'Name', 'tendeal' ) . ( $req ? ' <span class="text-danger">*</span>' : '' ) . '</label>
								<input id="author" name="author" type="text" class="form-control" value="' . esc_attr( $commenter['comment_author'] ) . '" placeholder="' . __( 'Your name', 'tendeal' ) . '"' . $aria_req . ' />
							</div>',
			'email'  => '<div class="col-md-6 mb-3">
							<label for="email" class="form-label">' . __( 'Email', 'tendeal' ) . ( $req ? ' <span class="text-danger">*</span>' : '' ) . '</label>
							<input id="email" name="email" type="email" class="form-control" value="' . esc_attr( $commenter['comment_author_email'] ) . '" placeholder="' . __( 'Your email address', 'tendeal' ) . '"' . $aria_req . ' />
						</div>
						</div>',
			'url'    => '<div class="mb-3">
							<label for="url" class="form-label">' . __( 'Website', 'tendeal' ) . '</label>
							<input id="url" name="url" type="url" class="form-control" value="' . esc_attr( $commenter['comment_author_url'] ) . '" placeholder="' . __( 'Your website (optional)', 'tendeal' ) . '" />
						</div>',
		),
		'class_form'           => 'comment-form bg-light p-4 rounded mt-4',
		'comment_notes_before' => '<div class="alert alert-info mb-3">
									<i class="fas fa-info-circle me-2"></i>
									' . __( 'Your email address will not be published. Required fields are marked with *', 'tendeal' ) . '
								</div>',
		'comment_notes_after'  => '',
	);

	comment_form( $comment_form_args );
	?>

</div><!-- #comments -->

<?php
/**
 * Custom comment callback for blog posts
 */
function tendeal_blog_comment_callback( $comment, $args, $depth ) {
	if ( 'div' === $args['style'] ) {
		$tag       = 'div';
		$add_below = 'comment';
	} else {
		$tag       = 'li';
		$add_below = 'div-comment';
	}
	?>
	<<?php echo $tag; ?> <?php comment_class( empty( $args['has_children'] ) ? '' : 'parent' ); ?> id="comment-<?php comment_ID(); ?>">
	<?php if ( 'div' !== $args['style'] ) : ?>
		<div id="div-comment-<?php comment_ID(); ?>" class="comment-body bg-light rounded p-3 mb-3">
	<?php endif; ?>
	
	<div class="comment-meta d-flex align-items-start mb-3">
		<div class="comment-author-avatar me-3">
			<?php
			if ( $args['avatar_size'] != 0 ) {
				echo get_avatar( $comment, 48, '', '', array( 'class' => 'rounded-circle' ) );
			}
			?>
		</div>
		<div class="comment-metadata flex-grow-1">
			<div class="comment-author-name">
				<strong><?php printf( __( '%s', 'tendeal' ), get_comment_author_link() ); ?></strong>
				<?php if ( '0' == $comment->comment_approved ) : ?>
					<span class="badge bg-warning ms-2"><?php _e( 'Your comment is awaiting moderation.', 'tendeal' ); ?></span>
				<?php endif; ?>
			</div>
			<div class="comment-date text-muted small">
				<i class="fas fa-clock me-1"></i>
				<a href="<?php echo htmlspecialchars( get_comment_link( $comment->comment_ID ) ); ?>" class="text-decoration-none text-muted">
					<?php
					/* translators: 1: date, 2: time */
					printf( __( '%1$s at %2$s', 'tendeal' ), get_comment_date(), get_comment_time() );
					?>
				</a>
			</div>
		</div>
		<div class="comment-actions">
			<?php
			comment_reply_link(
				array_merge(
					$args,
					array(
						'add_below' => $add_below,
						'depth'     => $depth,
						'max_depth' => $args['max_depth'],
						'class'     => 'btn btn-sm btn-outline-primary',
						'before'    => '<i class="fas fa-reply me-1"></i>',
					)
				)
			);
			?>
		</div>
	</div>

	<div class="comment-content">
		<?php comment_text(); ?>
	</div>

	<?php if ( 'div' !== $args['style'] ) : ?>
		</div>
	<?php endif; ?>
	<?php
}
?>
