<?php
/**
 * Cart Page - Optimized AliExpress Style with <PERSON>endor Grouping
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/cart/cart.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://docs.woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 7.9.0
 */

defined( 'ABSPATH' ) || exit;

// Enqueue optimized cart scripts and styles
wp_enqueue_script('tendeal-cart-optimized', get_stylesheet_directory_uri() . '/js/cart-optimized.js', array('jquery', 'wc-cart'), _S_VERSION, true);
wp_enqueue_style('tendeal-cart-optimized', get_stylesheet_directory_uri() . '/css/cart-optimized.css', array(), _S_VERSION);

// Localize script for AJAX
wp_localize_script('tendeal-cart-optimized', 'cartOptimized', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('cart_optimized_nonce'),
    'cart_url' => wc_get_cart_url(),
    'loading_text' => esc_html__('Loading...', 'tendeal'),
    'confirm_remove' => esc_html__('Are you sure you want to remove selected items?', 'tendeal'),
    'error_message' => esc_html__('An error occurred. Please try again.', 'tendeal'),
    'success_message' => esc_html__('Cart updated successfully.', 'tendeal'),
    'account_url' => wc_get_page_permalink('myaccount'),
    'no_shipping_methods' => esc_html__('No shipping methods available for this vendor.', 'tendeal'),
    'no_saved_addresses' => esc_html__('No saved addresses found. Add a new address to get started.', 'tendeal')
));

do_action( 'woocommerce_before_cart' ); ?>
<div class="cart-container container">
  <!-- Cart Messages -->
  <div id="cart-messages" class="cart-messages" style="display: none;">
    <div class="alert" role="alert">
      <span class="message-text"></span>
      <button type="button" class="btn-close" aria-label="Close"></button>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div id="cart-loading" class="cart-loading-overlay" style="display: none;">
    <div class="loading-spinner">
      <div class="spinner-border" role="status">
        <span class="visually-hidden"><?php esc_html_e('Loading...', 'tendeal'); ?></span>
      </div>
      <p><?php esc_html_e('Updating cart...', 'tendeal'); ?></p>
    </div>
  </div>

  <form class="woocommerce-cart-form" action="<?php echo esc_url( wc_get_cart_url() ); ?>" method="post" id="cart-form">
    <input type="hidden" name="update_cart" value="1">
    <?php wp_nonce_field('woocommerce-cart', 'woocommerce-cart-nonce'); ?>

    <div class="cart-header mt-4 pt-4 mb-2 d-flex align-items-center">
      <div class="cart-header-checkbox col-6 text-start">
        <label class="checkbox-container">
          <input type="checkbox" id="select-all" class="select-all-checkbox">
          <span class="checkmark"></span>
          <span class="checkbox-label"><?php esc_html_e('Select All', 'tendeal'); ?></span>
        </label>
      </div>
      <div class="cart-header-actions col-6 text-end">
        <button type="button" id="delete-selected-cart-items" class="btn btn-outline-danger btn-sm" disabled>
          <i data-feather="trash-2" class="feather-sm"></i>
          <span class="btn-text"><?php esc_html_e('Delete Selected', 'tendeal'); ?></span>
          <span class="selected-count">(0)</span>
        </button>
      </div>
    </div>
    <?php do_action( 'woocommerce_before_cart_table' ); ?>

    <?php
    // Group cart items by vendor
    $cart_items_by_vendor = array();
    $vendor_info = array();

    foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
        $_product   = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
        $product_id = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );

        if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_cart_item_visible', true, $cart_item, $cart_item_key ) ) {
            // Get vendor ID
            $vendor_id = 0;
            $vendor_name = __('Direct Sale', 'tendeal');
            $vendor_url = '';

            if ( function_exists( 'wcfmmp_get_store_url' ) ) {
                $vendor_id = get_post_field('post_author', $product_id);
                if ( $vendor_id ) {
                    $vendor_name = wcfm_get_vendor_store_name($vendor_id);
                    $vendor_url = wcfmmp_get_store_url($vendor_id);
                }
            }

            // Group items by vendor
            if ( !isset($cart_items_by_vendor[$vendor_id]) ) {
                $cart_items_by_vendor[$vendor_id] = array();
                $vendor_info[$vendor_id] = array(
                    'name' => $vendor_name,
                    'url' => $vendor_url,
                    'id' => $vendor_id
                );
            }

            $cart_items_by_vendor[$vendor_id][$cart_item_key] = $cart_item;
        }
    }

    do_action( 'woocommerce_before_cart_contents' );

    // Display cart items grouped by vendor
    foreach ( $cart_items_by_vendor as $vendor_id => $vendor_cart_items ) :
        $vendor = $vendor_info[$vendor_id];
        $vendor_subtotal = 0;

        // Calculate vendor subtotal
        foreach ( $vendor_cart_items as $cart_item_key => $cart_item ) {
            $_product = $cart_item['data'];
            $vendor_subtotal += $_product->get_price() * $cart_item['quantity'];
        }

        // Get available shipping methods from WooCommerce for this vendor
        $shipping_methods = tendeal_get_vendor_shipping_methods( $vendor_cart_items );
        ?>

    <!-- Vendor Section -->
    <div class="vendor-cart-section" data-vendor-id="<?php echo esc_attr($vendor_id); ?>">
      <div class="vendor-header">
        <div class="vendor-info">
          <input type="checkbox" class="vendor-select-all" data-vendor="<?php echo esc_attr($vendor_id); ?>">
          <div class="vendor-details">
            <h3 class="vendor-name">
              <i data-feather="store" class="feather-sm"></i>
              <?php if ( $vendor['url'] ) : ?>
              <a href="<?php echo esc_url($vendor['url']); ?>"><?php echo esc_html($vendor['name']); ?></a>
              <?php else : ?>
              <?php echo esc_html($vendor['name']); ?>
              <?php endif; ?>
            </h3>
            <div class="vendor-meta">
              <!-- <span class="vendor-item-count"><?php echo count($vendor_cart_items); ?>
                <?php _e('items', 'tendeal'); ?></span> -->
              <button type="button" class="shipping-selection-btn" data-vendor="<?php echo esc_attr($vendor_id); ?>">
                <i data-feather="truck" class="feather-sm"></i>
                <span class="shipping-text"><?php
                  // Get the default shipping method name
                  if ( !empty( $shipping_methods ) ) {
                    $first_method = reset( $shipping_methods );
                    echo esc_html( $first_method['name'] );
                  } else {
                    _e('Standard Shipping', 'tendeal');
                  }
                ?></span>
                <span class="shipping-cost"><?php
                  // Get the default shipping cost
                  if ( !empty( $shipping_methods ) ) {
                    $first_method = reset( $shipping_methods );
                    echo wc_price( $first_method['cost'] );
                  } else {
                    echo wc_price( 15.00 );
                  }
                ?></span>
                <i data-feather="chevron-down" class="feather-sm"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="vendor-actions">
          <button type="button" class="vendor-delete-all" data-vendor="<?php echo esc_attr($vendor_id); ?>">
            <i data-feather="trash-2" class="feather-sm"></i>
            <?php _e('Delete All', 'tendeal'); ?>
          </button>
        </div>
      </div>
      <!-- Vendor Products -->
      <div class="vendor-products">
        <?php foreach ( $vendor_cart_items as $cart_item_key => $cart_item ) :
                $_product   = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
                $product_id = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );
                $product_permalink = apply_filters( 'woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink( $cart_item ) : '', $cart_item, $cart_item_key );
                ?>

        <div
          class="cart-product-item <?php echo esc_attr( apply_filters( 'woocommerce_cart_item_class', 'cart_item', $cart_item, $cart_item_key ) ); ?>"
          data-cart-item-key="<?php echo esc_attr($cart_item_key); ?>">
          <!-- Product Selection Checkbox -->
          <div class="product-select">
            <input type="checkbox" class="cart-item-checkbox" name="cart_item_keys[]"
              value="<?php echo esc_attr( $cart_item_key ); ?>" data-vendor="<?php echo esc_attr($vendor_id); ?>">
          </div>

          <!-- Product Information -->
          <div class="product-info">
            <div class="product-thumbnail">
              <a href="<?php echo esc_url( $product_permalink ); ?>">
                <?php echo $_product->get_image(); ?>
              </a>
            </div>

            <div class="product-details">
              <a href="<?php echo esc_url( $product_permalink ); ?>"
                class="product-name"><?php echo $_product->get_name(); ?></a>

              <!-- Vendor Store Info -->
              <div class="vendor-store-info">
                <i data-feather="store"></i>
                <span><?php echo esc_html($vendor['name']); ?></span>
              </div>

              <!-- Product Reviews -->
              <?php
              $rating_count = $_product->get_rating_count();
              $review_count = $_product->get_review_count();
              $average_rating = $_product->get_average_rating();
              ?>
              <div class="product-reviews">
                <div class="star-rating">
                  <?php
                  if ( $review_count > 0 ) {
                    // Show filled stars based on rating
                    for ( $i = 1; $i <= 5; $i++ ) {
                      if ( $i <= $average_rating ) {
                        echo '<i data-feather="star" style="fill: #fbbf24; color: #fbbf24; width: 14px; height: 14px;"></i>';
                      } else {
                        echo '<i data-feather="star" style="color: #e5e7eb; width: 14px; height: 14px;"></i>';
                      }
                    }
                  } else {
                    // Show gray stars when no reviews
                    for ( $i = 1; $i <= 5; $i++ ) {
                      echo '<i data-feather="star" style="color: #d1d5db; width: 14px; height: 14px;"></i>';
                    }
                  }
                  ?>
                </div>
                <?php if ( $review_count > 0 ) : ?>
                <span class="rating"><?php echo esc_html( $average_rating ); ?></span>
                <span
                  class="review-count"><?php echo sprintf( _n( '%s Review', '%s Reviews', $review_count, 'tendeal' ), esc_html( $review_count ) ); ?></span>
                <?php else : ?>
                <span class="no-reviews"><?php _e('No reviews yet', 'tendeal'); ?></span>
                <?php endif; ?>
              </div>

              <!-- Quantity Control -->
              <div class="quantity-control">
                <button type="button" class="qty-btn decrease-qty"
                  data-cart-item-key="<?php echo esc_attr($cart_item_key); ?>"
                  aria-label="<?php esc_attr_e('Decrease quantity', 'tendeal'); ?>">
                  <i data-feather="minus" class="feather-xs"></i>
                </button>
                <input type="number" class="qty-input" name="cart[<?php echo $cart_item_key; ?>][qty]"
                  value="<?php echo esc_attr( $cart_item['quantity'] ); ?>" min="1"
                  max="<?php echo esc_attr( $_product->get_max_purchase_quantity() ); ?>" step="1"
                  data-cart-item-key="<?php echo esc_attr($cart_item_key); ?>"
                  data-product-id="<?php echo esc_attr($product_id); ?>"
                  aria-label="<?php esc_attr_e('Product quantity', 'tendeal'); ?>">
                <button type="button" class="qty-btn increase-qty"
                  data-cart-item-key="<?php echo esc_attr($cart_item_key); ?>"
                  aria-label="<?php esc_attr_e('Increase quantity', 'tendeal'); ?>">
                  <i data-feather="plus" class="feather-xs"></i>
                </button>
                <div class="product-actions">
                  <button type="button" class="remove-item-btn"
                    data-cart-item-key="<?php echo esc_attr($cart_item_key); ?>"
                    data-product-name="<?php echo esc_attr($_product->get_name()); ?>"
                    title="<?php esc_attr_e('Remove this item', 'tendeal'); ?>"
                    aria-label="<?php echo esc_attr(sprintf(__('Remove %s from cart', 'tendeal'), $_product->get_name())); ?>">
                    <i data-feather="trash-2" class="feather-sm"></i>
                    <span class="btn-text"><?php esc_html_e('Remove', 'tendeal'); ?></span>
                  </button>
                </div>
              </div>

            </div>
          </div>

          <!-- Product Price Section -->
          <div class="product-price-section">
            <div class="product-subtotal">
              <?php echo apply_filters( 'woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal( $_product, $cart_item['quantity'] ), $cart_item, $cart_item_key ); ?>
            </div>
            <?php
            // Calculate tax if applicable
            $tax_rate = 0.05; // 5% tax rate - you can make this dynamic
            $item_total = $_product->get_price() * $cart_item['quantity'];
            $tax_amount = $item_total * $tax_rate;
            if ( $tax_amount > 0 ) :
            ?>
            <!-- <div class="product-tax">
              <?php printf( __('Tax: %s', 'tendeal'), wc_price( $tax_amount ) ); ?>
            </div> -->
            <?php endif; ?>
          </div>

          <!-- Product Actions -->
          <!-- <div class="product-actions">
            <a href="<?php echo esc_url( wc_get_cart_remove_url( $cart_item_key ) ); ?>" class="remove-item">
              <i data-feather="trash-2"></i>
              <?php _e('Delete item', 'tendeal'); ?>
            </a>
          </div> -->
        </div>
        <?php endforeach; ?>
      </div>


      <!-- Hidden shipping methods data for modal -->
      <div class="vendor-shipping-data" style="display: none;" data-vendor="<?php echo esc_attr($vendor_id); ?>">
        <?php
        $first_method = true;
        foreach ( $shipping_methods as $method_id => $method ) :
        ?>
        <input type="radio" id="shipping_<?php echo esc_attr($vendor_id); ?>_<?php echo esc_attr($method_id); ?>"
          name="vendor_shipping[<?php echo esc_attr($vendor_id); ?>]" value="<?php echo esc_attr($method_id); ?>"
          data-cost="<?php echo esc_attr($method['cost']); ?>" data-name="<?php echo esc_attr($method['name']); ?>"
          data-time="<?php echo esc_attr($method['time']); ?>"
          data-description="<?php echo esc_attr($method['description']); ?>"
          <?php echo $first_method ? 'checked' : ''; ?>>
        <?php
        $first_method = false;
        endforeach; ?>
      </div>

      <!-- Vendor Subtotal -->
      <!-- <div class="vendor-subtotal">
        <div class="subtotal-row">
          <span class="subtotal-label"><?php _e('Subtotal', 'tendeal'); ?>:</span>
          <span class="subtotal-amount"><?php echo wc_price($vendor_subtotal); ?></span>
        </div>
        <div class="shipping-row">
          <span class="shipping-label"><?php _e('Shipping', 'tendeal'); ?>:</span>
          <span class="shipping-amount" data-vendor="<?php echo esc_attr($vendor_id); ?>">
            <?php
            if ( !empty( $shipping_methods ) ) {
              $first_method = reset( $shipping_methods );
              echo wc_price( $first_method['cost'] );
              $default_shipping_cost = $first_method['cost'];
            } else {
              echo wc_price( 15.00 );
              $default_shipping_cost = 15.00;
            }
            ?>
          </span>
        </div>
        <div class="vendor-total-row">
          <span class="total-label"><?php _e('Total', 'tendeal'); ?>:</span>
          <span class="total-amount"
            data-vendor="<?php echo esc_attr($vendor_id); ?>"><?php echo wc_price($vendor_subtotal + $default_shipping_cost); ?></span>
        </div>
      </div> -->
    </div>

    <?php endforeach; ?>

    <?php do_action( 'woocommerce_cart_contents' ); ?>
    <?php do_action( 'woocommerce_after_cart_contents' ); ?>
    <?php do_action( 'woocommerce_after_cart_table' ); ?>
  </form>

  <aside class="cart-sidebar mt-4">
    <!-- Address Box -->
    <div class="cart-address-box">
      <h4><?php esc_html_e( 'Delivery Address', 'tendeal' ); ?></h4>
      <?php
      // Get customer address information
      $customer = WC()->customer;
      $has_shipping_address = false;
      $address_display = '';

      if ( is_user_logged_in() ) {
        // Check if customer has shipping address
        $shipping_address = array(
          'first_name' => $customer->get_shipping_first_name(),
          'last_name'  => $customer->get_shipping_last_name(),
          'company'    => $customer->get_shipping_company(),
          'address_1'  => $customer->get_shipping_address_1(),
          'address_2'  => $customer->get_shipping_address_2(),
          'city'       => $customer->get_shipping_city(),
          'state'      => $customer->get_shipping_state(),
          'postcode'   => $customer->get_shipping_postcode(),
          'country'    => $customer->get_shipping_country(),
        );

        // If no shipping address, fall back to billing
        if ( empty( array_filter( $shipping_address ) ) ) {
          $shipping_address = array(
            'first_name' => $customer->get_billing_first_name(),
            'last_name'  => $customer->get_billing_last_name(),
            'company'    => $customer->get_billing_company(),
            'address_1'  => $customer->get_billing_address_1(),
            'address_2'  => $customer->get_billing_address_2(),
            'city'       => $customer->get_billing_city(),
            'state'      => $customer->get_billing_state(),
            'postcode'   => $customer->get_billing_postcode(),
            'country'    => $customer->get_billing_country(),
          );
        }

        // Check if we have any address data
        $has_shipping_address = !empty( array_filter( $shipping_address ) );

        if ( $has_shipping_address ) {
          $formatted_address = WC()->countries->get_formatted_address( $shipping_address );
          $address_display = $formatted_address;
        }
      }
      ?>

      <div class="address-content">
        <?php if ( $has_shipping_address && !empty( $address_display ) ) : ?>
        <div class="current-address">
          <div class="address-icon">
            <i data-feather="map-pin"></i>
          </div>
          <div class="address-text">
            <?php echo wp_kses_post( $address_display ); ?>
          </div>
        </div>
        <?php else : ?>
        <div class="no-address">
          <div class="address-icon">
            <i data-feather="map-pin"></i>
          </div>
          <div class="address-text">
            <p><?php esc_html_e( 'No delivery address set', 'tendeal' ); ?></p>
          </div>
        </div>
        <?php endif; ?>

        <div class="address-actions">
          <?php if ( is_user_logged_in() ) : ?>
          <button type="button" class="btn-select-address" id="select-saved-address">
            <i data-feather="list"></i>
            <?php esc_html_e( 'Select Address', 'tendeal' ); ?>
          </button>
          <button type="button" class="btn-update-address" id="toggle-address-form">
            <i data-feather="edit-2"></i>
            <?php esc_html_e( 'Update Address', 'tendeal' ); ?>
          </button>
          <?php else : ?>
          <a href="<?php echo esc_url( wc_get_page_permalink( 'myaccount' ) ); ?>" class="btn-update-address">
            <i data-feather="user"></i>
            <?php esc_html_e( 'Login to set address', 'tendeal' ); ?>
          </a>
          <?php endif; ?>
        </div>

        <!-- Address Update Form (Hidden by default) -->
        <?php if ( is_user_logged_in() ) : ?>
        <div class="address-update-form" id="address-update-form" style="display: none;">
          <form id="cart-address-form">
            <?php wp_nonce_field( 'update_cart_address', 'cart_address_nonce' ); ?>

            <div class="form-row">
              <div class="form-group half">
                <label for="shipping_first_name"><?php esc_html_e( 'First Name', 'tendeal' ); ?> *</label>
                <input type="text" id="shipping_first_name" name="shipping_first_name"
                  value="<?php echo esc_attr( $customer->get_shipping_first_name() ?: $customer->get_billing_first_name() ); ?>"
                  required>
              </div>
              <div class="form-group half">
                <label for="shipping_last_name"><?php esc_html_e( 'Last Name', 'tendeal' ); ?> *</label>
                <input type="text" id="shipping_last_name" name="shipping_last_name"
                  value="<?php echo esc_attr( $customer->get_shipping_last_name() ?: $customer->get_billing_last_name() ); ?>"
                  required>
              </div>
            </div>

            <div class="form-group">
              <label for="shipping_address_1"><?php esc_html_e( 'Address Line 1', 'tendeal' ); ?> *</label>
              <input type="text" id="shipping_address_1" name="shipping_address_1"
                value="<?php echo esc_attr( $customer->get_shipping_address_1() ?: $customer->get_billing_address_1() ); ?>"
                required>
            </div>

            <div class="form-group">
              <label for="shipping_address_2"><?php esc_html_e( 'Address Line 2', 'tendeal' ); ?></label>
              <input type="text" id="shipping_address_2" name="shipping_address_2"
                value="<?php echo esc_attr( $customer->get_shipping_address_2() ?: $customer->get_billing_address_2() ); ?>">
            </div>

            <div class="form-row">
              <div class="form-group half">
                <label for="shipping_city"><?php esc_html_e( 'City', 'tendeal' ); ?> *</label>
                <input type="text" id="shipping_city" name="shipping_city"
                  value="<?php echo esc_attr( $customer->get_shipping_city() ?: $customer->get_billing_city() ); ?>"
                  required>
              </div>
              <div class="form-group half">
                <label for="shipping_postcode"><?php esc_html_e( 'Postal Code', 'tendeal' ); ?></label>
                <input type="text" id="shipping_postcode" name="shipping_postcode"
                  value="<?php echo esc_attr( $customer->get_shipping_postcode() ?: $customer->get_billing_postcode() ); ?>">
              </div>
            </div>

            <div class="form-row">
              <div class="form-group half">
                <label for="shipping_state"><?php esc_html_e( 'State/Province', 'tendeal' ); ?></label>
                <input type="text" id="shipping_state" name="shipping_state"
                  value="<?php echo esc_attr( $customer->get_shipping_state() ?: $customer->get_billing_state() ); ?>">
              </div>
              <div class="form-group half">
                <label for="shipping_country"><?php esc_html_e( 'Country', 'tendeal' ); ?> *</label>
                <select id="shipping_country" name="shipping_country" required>
                  <?php
                    $countries = WC()->countries->get_countries();
                    $selected_country = $customer->get_shipping_country() ?: $customer->get_billing_country();
                    foreach ( $countries as $code => $name ) {
                      echo '<option value="' . esc_attr( $code ) . '"' . selected( $selected_country, $code, false ) . '>' . esc_html( $name ) . '</option>';
                    }
                    ?>
                </select>
              </div>
            </div>

            <div class="form-actions">
              <button type="button" class="btn-cancel" id="cancel-address-form">
                <i data-feather="x"></i>
                <?php esc_html_e( 'Cancel', 'tendeal' ); ?>
              </button>
              <button type="submit" class="btn-save">
                <i data-feather="check"></i>
                <?php esc_html_e( 'Save Address', 'tendeal' ); ?>
              </button>
            </div>
          </form>
        </div>
        <?php endif; ?>
      </div>
    </div>

    <!-- Coupon Box -->
    <div class="cart-coupon">
      <span>Coupon Code</span>
      <input type="text" id="coupon_code" placeholder="Enter coupon code">
      <button type="button" id="apply_coupon" class="button">Apply Coupon</button>
      <p id="coupon_message" class="coupon-message"></p>
    </div>

    <!-- Enhanced Cart Totals with Vendor Breakdown -->
    <div class="cart-simple-totals">
      <h4><?php esc_html_e( 'Order Summary', 'tendeal' ); ?></h4>
      <div class="totals-content">
        <?php
        // Get cart totals
        $cart = WC()->cart;
        $subtotal = $cart->get_subtotal();
        $discount_total = $cart->get_discount_total();
        $applied_coupons = $cart->get_applied_coupons();

        // Calculate total shipping from all vendors (use actual default shipping costs)
        $total_vendor_shipping = 0;
        foreach ( $cart_items_by_vendor as $v_id => $v_items ) {
          // Get actual shipping methods for this vendor
          $vendor_shipping_methods = tendeal_get_vendor_shipping_methods( $v_items );
          if ( !empty( $vendor_shipping_methods ) ) {
            $first_method = reset( $vendor_shipping_methods );
            $total_vendor_shipping += $first_method['cost'];
          } else {
            $total_vendor_shipping += 15.00; // Fallback cost
          }
        }

        // Calculate final total
        $final_total = $subtotal + $total_vendor_shipping - $discount_total;
        ?>

        <!-- Subtotal -->
        <div class="summary-row subtotal">
          <span class="total-label">
            <i data-feather="shopping-cart" class="feather-sm"></i>
            <?php esc_html_e( 'Subtotal', 'tendeal' ); ?>
          </span>
          <span class="amount cart-subtotal">
            <?php echo wp_kses_post( wc_price( $subtotal ) ); ?>
          </span>
        </div>

        <!-- Vendor Shipping Breakdown -->
        <div class="summary-row shipping">
          <span class="total-label">
            <i data-feather="truck" class="feather-sm"></i>
            <?php esc_html_e( 'Shipping', 'tendeal' ); ?>
            <small>(<?php echo count($cart_items_by_vendor); ?> <?php _e('vendors', 'tendeal'); ?>)</small>
          </span>
          <span class="amount shipping-total">
            <?php echo wp_kses_post( wc_price( $total_vendor_shipping ) ); ?>
          </span>
        </div>

        <!-- Discount (only show if coupons are applied) -->
        <div class="summary-row discount-row"
          style="<?php echo empty($applied_coupons) || $discount_total <= 0 ? 'display: none;' : ''; ?>">
          <span class="total-label">
            <i data-feather="tag" class="feather-sm"></i>
            <?php esc_html_e( 'Discount', 'tendeal' ); ?>
            <?php if ( !empty($applied_coupons) && count( $applied_coupons ) === 1 ) : ?>
            <small>(<?php echo esc_html( $applied_coupons[0] ); ?>)</small>
            <?php endif; ?>
          </span>
          <span class="amount discount-total">
            -<?php echo wp_kses_post( wc_price( $discount_total ) ); ?>
          </span>
        </div>

        <!-- Total -->
        <div class="summary-row total">
          <span class="total-label">
            <i data-feather="credit-card" class="feather-sm"></i>
            <?php esc_html_e( 'Total', 'tendeal' ); ?>
          </span>
          <span class="amount cart-total">
            <?php echo wp_kses_post( wc_price( $final_total ) ); ?>
          </span>
        </div>

        <div class="checkout-button-wrapper">
          <?php if (current_user_can('manage_options')) : ?>
          <!-- <button type="button" id="test-cart-calculation" class="btn btn-secondary mb-2" style="width: 100%;">
            🔧 Test Cart Calculation (Admin Only)
          </button> -->
          <?php endif; ?>
          <a href="<?php echo esc_url( wc_get_checkout_url() ); ?>" class="btn-checkout">
            <i data-feather="credit-card"></i>
            <?php esc_html_e( 'Proceed to Checkout', 'tendeal' ); ?>
          </a>
        </div>
      </div>
    </div>


    <div class="payment-methods">
      <h2><?php esc_html_e( 'Payment Methods', 'woocommerce' ); ?></h2>
      <p><?php esc_html_e( 'Accepted payment methods will be displayed during checkout.', 'woocommerce' ); ?></p>
      <?php
    // In a real scenario, you might display payment gateway icons or information here.
    // This is highly theme-dependent and often handled in the checkout process.
    // For example, you could retrieve available payment gateways:
    /*
    $available_gateways = WC()->payment_gateways->get_available_payment_gateways();
    if ( ! empty( $available_gateways ) ) {
        echo '<ul>';
        foreach ( $available_gateways as $gateway ) {
            echo '<li>' . esc_html( $gateway->title ) . '</li>';
        }
        echo '</ul>';
    } else {
        echo '<p>' . esc_html__( 'No payment methods available.', 'woocommerce' ) . '</p>';
    }
    */
    ?>
    </div>


  </aside>
</div>

<!-- Shipping Selection Modal -->
<div id="shipping-modal" class="shipping-modal" style="display: none;">
  <div class="shipping-modal-overlay"></div>
  <div class="shipping-modal-content">
    <div class="shipping-modal-header">
      <h3 class="modal-title">
        <i data-feather="truck" class="feather-sm"></i>
        <?php _e('Select Shipping Method', 'tendeal'); ?>
      </h3>
      <button type="button" class="modal-close" id="close-shipping-modal">
        <i data-feather="x" class="feather-sm"></i>
      </button>
    </div>

    <div class="shipping-modal-body">
      <div class="vendor-name-display">
        <i data-feather="store" class="feather-sm"></i>
        <span id="modal-vendor-name"></span>
      </div>

      <div class="shipping-methods-list" id="modal-shipping-methods">
        <!-- Shipping methods will be populated by JavaScript -->
      </div>
    </div>

    <div class="shipping-modal-footer">
      <button type="button" class="btn-cancel" id="cancel-shipping-selection">
        <?php _e('Cancel', 'tendeal'); ?>
      </button>
      <button type="button" class="btn-confirm" id="confirm-shipping-selection">
        <?php _e('Confirm Selection', 'tendeal'); ?>
      </button>
    </div>
  </div>
</div>

<?php do_action( 'woocommerce_before_cart_collaterals' ); ?>
<?php do_action( 'woocommerce_after_cart' ); ?>



<script>
// Select All functionality
document.getElementById('select-all').addEventListener('change', function() {
  let checkboxes = document.querySelectorAll('.cart-item-checkbox');
  let vendorCheckboxes = document.querySelectorAll('.vendor-select-all');

  checkboxes.forEach(checkbox => checkbox.checked = this.checked);
  vendorCheckboxes.forEach(checkbox => checkbox.checked = this.checked);
});

// Vendor Select All functionality
document.querySelectorAll('.vendor-select-all').forEach(vendorCheckbox => {
  vendorCheckbox.addEventListener('change', function() {
    const vendorId = this.dataset.vendor;
    const vendorItems = document.querySelectorAll(`.cart-item-checkbox[data-vendor="${vendorId}"]`);

    vendorItems.forEach(checkbox => checkbox.checked = this.checked);

    // Update main select all checkbox
    updateMainSelectAll();
  });
});

// Individual item checkbox functionality
document.querySelectorAll('.cart-item-checkbox').forEach(itemCheckbox => {
  itemCheckbox.addEventListener('change', function() {
    const vendorId = this.dataset.vendor;
    updateVendorSelectAll(vendorId);
    updateMainSelectAll();
  });
});

// Update vendor select all checkbox based on individual items
function updateVendorSelectAll(vendorId) {
  const vendorItems = document.querySelectorAll(`.cart-item-checkbox[data-vendor="${vendorId}"]`);
  const vendorCheckbox = document.querySelector(`.vendor-select-all[data-vendor="${vendorId}"]`);

  if (vendorCheckbox) {
    const checkedItems = document.querySelectorAll(`.cart-item-checkbox[data-vendor="${vendorId}"]:checked`);
    vendorCheckbox.checked = checkedItems.length === vendorItems.length;
  }
}

// Update main select all checkbox
function updateMainSelectAll() {
  const allItems = document.querySelectorAll('.cart-item-checkbox');
  const checkedItems = document.querySelectorAll('.cart-item-checkbox:checked');
  const mainSelectAll = document.getElementById('select-all');

  if (mainSelectAll) {
    mainSelectAll.checked = checkedItems.length === allItems.length;
  }
}

// Quantity controls
document.querySelectorAll('.decrease-qty').forEach(button => {
  button.addEventListener('click', function() {
    let input = this.nextElementSibling;
    if (input.value > 1) {
      input.value--;
      updateCartQuantity(input);
    }
  });
});

document.querySelectorAll('.increase-qty').forEach(button => {
  button.addEventListener('click', function() {
    let input = this.previousElementSibling;
    input.value++;
    updateCartQuantity(input);
  });
});

function updateCartQuantity(input) {
  // Trigger change event to update cart
  const event = new Event('change', {
    bubbles: true
  });
  input.dispatchEvent(event);

  // Submit the form after a short delay
  setTimeout(function() {
    document.querySelector('.woocommerce-cart-form').submit();
  }, 300);
}


// Shipping selection modal functionality
let currentVendorId = null;
let currentVendorName = '';

// Open shipping modal when clicking shipping selection button
document.querySelectorAll('.shipping-selection-btn').forEach(button => {
  button.addEventListener('click', function() {
    currentVendorId = this.dataset.vendor;
    currentVendorName = this.closest('.vendor-details').querySelector('.vendor-name').textContent.trim();

    openShippingModal(currentVendorId, currentVendorName);
  });
});

function openShippingModal(vendorId, vendorName) {
  const modal = document.getElementById('shipping-modal');
  const modalVendorName = document.getElementById('modal-vendor-name');
  const modalShippingMethods = document.getElementById('modal-shipping-methods');

  // Set vendor name in modal
  modalVendorName.textContent = vendorName;

  // Get shipping data for this vendor
  const vendorShippingData = document.querySelector(`.vendor-shipping-data[data-vendor="${vendorId}"]`);
  const shippingInputs = vendorShippingData.querySelectorAll('input[type="radio"]');

  // Clear previous methods
  modalShippingMethods.innerHTML = '';

  // Populate shipping methods in modal
  shippingInputs.forEach(input => {
    const methodDiv = document.createElement('div');
    methodDiv.className = 'modal-shipping-method';

    const isChecked = input.checked ? 'checked' : '';
    const activeClass = input.checked ? 'active' : '';

    methodDiv.innerHTML = `
      <input type="radio"
             id="modal_${input.id}"
             name="modal_vendor_shipping_${vendorId}"
             value="${input.value}"
             data-cost="${input.dataset.cost}"
             data-name="${input.dataset.name}"
             data-time="${input.dataset.time}"
             data-description="${input.dataset.description}"
             ${isChecked}>
      <label for="modal_${input.id}" class="modal-shipping-label ${activeClass}">
        <div class="method-info">
          <div class="method-name">${input.dataset.name}</div>
          <div class="method-details">
            <span class="method-time">${input.dataset.time}</span>
            <span class="method-description">${input.dataset.description}</span>
          </div>
        </div>
        <div class="method-cost">${parseFloat(input.dataset.cost).toFixed(2)} QAR</div>
      </label>
    `;

    modalShippingMethods.appendChild(methodDiv);
  });

  // Add event listeners to modal radio buttons
  modalShippingMethods.querySelectorAll('input[type="radio"]').forEach(radio => {
    radio.addEventListener('change', function() {
      // Remove active class from all labels
      modalShippingMethods.querySelectorAll('.modal-shipping-label').forEach(label => {
        label.classList.remove('active');
      });

      // Add active class to selected label
      this.nextElementSibling.classList.add('active');

      // Enable confirm button
      const confirmBtn = document.getElementById('confirm-shipping-method');
      if (confirmBtn) {
        confirmBtn.disabled = false;
      }
    });
  });

  // Show modal
  modal.style.display = 'block';
  document.body.style.overflow = 'hidden';

  // Initialize feather icons in modal
  if (typeof feather !== 'undefined') {
    feather.replace();
  }
}

// Close modal functionality
function closeShippingModal() {
  const modal = document.getElementById('shipping-modal');
  modal.style.display = 'none';
  document.body.style.overflow = 'auto';
  currentVendorId = null;
  currentVendorName = '';
}

document.getElementById('close-shipping-modal').addEventListener('click', closeShippingModal);
document.getElementById('cancel-shipping-selection').addEventListener('click', closeShippingModal);

// Close modal when clicking overlay
document.querySelector('.shipping-modal-overlay').addEventListener('click', closeShippingModal);

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape' && document.getElementById('shipping-modal').style.display === 'block') {
    closeShippingModal();
  }
});

// Confirm shipping selection
document.getElementById('confirm-shipping-selection').addEventListener('click', function() {
  if (!currentVendorId) return;

  const selectedMethod = document.querySelector(`input[name="modal_vendor_shipping_${currentVendorId}"]:checked`);
  if (!selectedMethod) return;

  // Update the original hidden radio button
  const originalInput = document.querySelector(
    `input[name="vendor_shipping[${currentVendorId}]"][value="${selectedMethod.value}"]`);
  if (originalInput) {
    originalInput.checked = true;
  }

  // Update the shipping selection button display
  const shippingBtn = document.querySelector(`.shipping-selection-btn[data-vendor="${currentVendorId}"]`);
  if (shippingBtn) {
    const shippingText = shippingBtn.querySelector('.shipping-text');
    const shippingCost = shippingBtn.querySelector('.shipping-cost');

    shippingText.textContent = selectedMethod.dataset.name;
    shippingCost.textContent = parseFloat(selectedMethod.dataset.cost).toFixed(2) + ' QAR';
  }

  // Update totals immediately for better UX
  updateVendorTotals(currentVendorId, parseFloat(selectedMethod.dataset.cost));

  // Save shipping method selection to server
  saveShippingMethodSelection(currentVendorId, selectedMethod.value);

  // Use the optimized AJAX system to update shipping method
  if (window.cartOptimized && window.cartOptimized.functions && window.cartOptimized.functions.refreshCartTotals) {
    // Call the optimized refresh function to get proper totals from server
    window.cartOptimized.functions.refreshCartTotals();
  } else {
    // Fallback to simple total update
    updateCartTotals();
  }

  // Close modal
  closeShippingModal();
});

// Update vendor totals when shipping changes
function updateVendorTotals(vendorId, shippingCost) {
  const shippingAmount = document.querySelector(`.shipping-amount[data-vendor="${vendorId}"]`);
  const totalAmount = document.querySelector(`.total-amount[data-vendor="${vendorId}"]`);

  if (shippingAmount && totalAmount) {
    shippingAmount.textContent = shippingCost.toFixed(2) + ' QAR';

    // Recalculate vendor total
    const subtotalElement = shippingAmount.closest('.vendor-subtotal').querySelector('.subtotal-amount');
    if (subtotalElement) {
      const subtotal = parseFloat(subtotalElement.textContent.replace(/[^\d.]/g, ''));
      const newTotal = subtotal + shippingCost;
      totalAmount.textContent = newTotal.toFixed(2) + ' QAR';
    }
  }

  // Update the main cart totals
  updateMainCartTotals();
}

// Function to update main cart totals based on selected shipping methods
function updateMainCartTotals() {
  let totalShipping = 0;

  // Calculate total shipping from all selected methods
  document.querySelectorAll('input[name^="vendor_shipping"]:checked').forEach(input => {
    const cost = parseFloat(input.dataset.cost || 0);
    totalShipping += cost;
    console.log('Adding shipping cost:', cost, 'for vendor:', input.name);
  });

  console.log('Total shipping calculated:', totalShipping);

  // Update shipping total display
  const shippingTotalElement = document.querySelector('.shipping-total');
  if (shippingTotalElement) {
    // Format price properly
    const formattedShipping = totalShipping.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }) + ' QAR';
    shippingTotalElement.innerHTML = formattedShipping;
    console.log('Updated shipping total display to:', formattedShipping);
  }

  // Get cart subtotal
  const subtotalElement = document.querySelector('.cart-subtotal');
  let subtotal = 0;
  if (subtotalElement) {
    const subtotalText = subtotalElement.textContent || subtotalElement.innerHTML;
    subtotal = parseFloat(subtotalText.replace(/[^\d.]/g, ''));
    console.log('Cart subtotal:', subtotal);
  }

  // Get discount total
  const discountElement = document.querySelector('.discount-row .amount');
  let discount = 0;
  if (discountElement && !discountElement.closest('.discount-row').style.display.includes('none')) {
    const discountText = discountElement.textContent || discountElement.innerHTML;
    discount = parseFloat(discountText.replace(/[^\d.]/g, ''));
    console.log('Discount amount:', discount);
  }

  // Calculate and update final total
  const finalTotal = subtotal + totalShipping - discount;
  console.log('Final total calculated:', finalTotal);

  const cartTotalElement = document.querySelector('.cart-total');
  if (cartTotalElement) {
    const formattedTotal = finalTotal.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }) + ' QAR';
    cartTotalElement.innerHTML = formattedTotal;
    console.log('Updated cart total display to:', formattedTotal);
  }
}

// Vendor delete all functionality
document.querySelectorAll('.vendor-delete-all').forEach(button => {
  button.addEventListener('click', function() {
    const vendorId = this.dataset.vendor;
    const vendorItems = document.querySelectorAll(`.cart-item-checkbox[data-vendor="${vendorId}"]`);

    if (confirm('Are you sure you want to delete all items from this vendor?')) {
      // Check all items for this vendor
      vendorItems.forEach(checkbox => checkbox.checked = true);

      // Trigger delete selected items
      document.getElementById('delete-selected-cart-items').click();
    }
  });
});

// Cart totals are now handled by cart-optimized.js
// This function is kept for compatibility but delegates to the optimized system
function updateCartTotals() {
  // Use the optimized cart system if available
  if (window.cartOptimized && window.cartOptimized.functions && window.cartOptimized.functions.refreshCartTotals) {
    window.cartOptimized.functions.refreshCartTotals();
  }
}

// Save shipping method selection to server
function saveShippingMethodSelection(vendorId, shippingMethod) {
  // Use the cart optimized AJAX system if available
  if (window.cartOptimized && window.cartOptimized.ajax_url) {
    $.ajax({
      url: window.cartOptimized.ajax_url,
      type: 'POST',
      data: {
        action: 'update_vendor_shipping_method',
        vendor_id: vendorId,
        shipping_method: shippingMethod,
        nonce: window.cartOptimized.nonce
      },
      success: function(response) {
        if (response.success) {
          console.log('Shipping method saved successfully for vendor:', vendorId);
        } else {
          console.warn('Failed to save shipping method:', response.data);
        }
      },
      error: function(xhr, status, error) {
        console.error('Error saving shipping method:', error);
      }
    });
  } else {
    // Fallback AJAX call
    $.ajax({
      url: '<?php echo admin_url('admin-ajax.php'); ?>',
      type: 'POST',
      data: {
        action: 'update_vendor_shipping_method',
        vendor_id: vendorId,
        shipping_method: shippingMethod,
        nonce: '<?php echo wp_create_nonce('cart_optimized_nonce'); ?>'
      },
      success: function(response) {
        if (response.success) {
          console.log('Shipping method saved successfully for vendor:', vendorId);
        }
      },
      error: function(xhr, status, error) {
        console.error('Error saving shipping method:', error);
      }
    });
  }
}

// Debug function to check shipping methods
function debugShippingMethods() {
  console.log('=== Shipping Methods Debug ===');
  document.querySelectorAll('input[name^="vendor_shipping"]').forEach(input => {
    console.log('Shipping input:', {
      name: input.name,
      value: input.value,
      cost: input.dataset.cost,
      checked: input.checked,
      methodName: input.dataset.name
    });
  });
  console.log('=== End Debug ===');
}

// Initialize cart totals on page load
document.addEventListener('DOMContentLoaded', function() {
  console.log('Cart page loaded, initializing totals...');

  // Debug shipping methods
  debugShippingMethods();

  // Ensure all shipping methods are properly initialized
  updateMainCartTotals();

  // Add event listeners to all shipping method radio buttons
  document.querySelectorAll('input[name^="vendor_shipping"]').forEach(input => {
    input.addEventListener('change', function() {
      console.log('Shipping method changed:', this.name, this.value, this.dataset.cost);
      updateMainCartTotals();
    });
  });

  // Add debug button for testing (remove in production)
  if (window.location.search.includes('debug=1')) {
    const debugBtn = document.createElement('button');
    debugBtn.textContent = 'Debug Shipping';
    debugBtn.style.position = 'fixed';
    debugBtn.style.top = '10px';
    debugBtn.style.right = '10px';
    debugBtn.style.zIndex = '9999';
    debugBtn.onclick = debugShippingMethods;
    document.body.appendChild(debugBtn);
  }
});

// Initialize vendor cart functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Initialize feather icons
  if (typeof feather !== 'undefined') {
    feather.replace();
  }

  // Cart totals are handled by cart-optimized.js
  console.log('Cart template loaded - totals handled by optimized system');

  // Add test button functionality for admins
  const testButton = document.getElementById('test-cart-calculation');
  if (testButton) {
    testButton.addEventListener('click', function() {
      fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            action: 'test_cart_calculation',
            nonce: '<?php echo wp_create_nonce('cart_optimized_nonce'); ?>'
          })
        })
        .then(response => response.json())
        .then(data => {
          console.log('Cart Calculation Test Results:', data);
          if (data.success) {
            const results = data.data;
            alert(`Cart Calculation Test Results:

Raw Values:
- Subtotal: ${results.raw_values.subtotal}
- Shipping: ${results.raw_values.shipping}
- Tax: ${results.raw_values.tax}
- Discount: ${results.raw_values.discount}

Calculations:
- Manual Total: ${results.calculations.manual_total}
- WC Total: ${results.calculations.wc_total}
- Match: ${results.calculations.match ? 'YES' : 'NO'}

Formatted Values:
- Subtotal: ${results.formatted_values.subtotal}
- Shipping: ${results.formatted_values.shipping}
- Total: ${results.formatted_values.wc_total}`);
          } else {
            alert('Test failed: ' + data.data);
          }
        })
        .catch(error => {
          console.error('Test error:', error);
          alert('Test failed with error');
        });
    });
  }
});

jQuery(document).ready(function($) {
  // Initialize Feather icons
  if (typeof feather !== 'undefined') {
    feather.replace();
  }

  // Address form toggle functionality
  $('#toggle-address-form').click(function() {
    var $form = $('#address-update-form');
    var $button = $(this);

    if ($form.is(':visible')) {
      $form.slideUp(300);
      $button.find('i').attr('data-feather', 'edit-2');
      $button.find('span').text('<?php esc_html_e( 'Update Address', 'tendeal' ); ?>');
    } else {
      $form.slideDown(300);
      $button.find('i').attr('data-feather', 'x');
      $button.find('span').text('<?php esc_html_e( 'Cancel', 'tendeal' ); ?>');
    }

    // Re-initialize feather icons
    if (typeof feather !== 'undefined') {
      feather.replace();
    }
  });

  // Cancel address form
  $('#cancel-address-form').click(function() {
    $('#address-update-form').slideUp(300);
    var $toggleButton = $('#toggle-address-form');
    $toggleButton.find('i').attr('data-feather', 'edit-2');
    $toggleButton.find('span').text('<?php esc_html_e( 'Update Address', 'tendeal' ); ?>');

    // Re-initialize feather icons
    if (typeof feather !== 'undefined') {
      feather.replace();
    }
  });

  // Handle address form submission
  $('#cart-address-form').submit(function(e) {
    e.preventDefault();

    var formData = $(this).serialize();
    formData += '&action=update_cart_address';

    // Show loading state
    var $saveButton = $('.btn-save');
    var originalText = $saveButton.html();
    $saveButton.html('<i data-feather="loader"></i> Saving...').prop('disabled', true);

    if (typeof feather !== 'undefined') {
      feather.replace();
    }

    $.ajax({
      url: wc_ajax_params.ajax_url,
      type: 'POST',
      data: formData,
      success: function(response) {
        if (response.success) {
          // Show success message
          alert('Address updated successfully!');
          // Reload the page to show updated address
          location.reload();
        } else {
          alert('Error: ' + (response.data.message || 'Failed to update address'));
          $saveButton.html(originalText).prop('disabled', false);
          if (typeof feather !== 'undefined') {
            feather.replace();
          }
        }
      },
      error: function() {
        alert('Error: Failed to update address. Please try again.');
        $saveButton.html(originalText).prop('disabled', false);
        if (typeof feather !== 'undefined') {
          feather.replace();
        }
      }
    });
  });

  // Fix for select-all checkbox
  $('#select-all').click(function() {
    $('.cart-item-checkbox').prop('checked', $(this).prop('checked'));
  });

  $('#delete-selected-cart-items').click(function() {
    var selectedItems = [];
    $('.cart-item-checkbox:checked').each(function() {
      selectedItems.push($(this).val());
    });

    if (selectedItems.length > 0) {
      // Use standard jQuery AJAX, and ensure data is correctly formatted
      $.ajax({
        type: 'POST',
        url: wc_ajax_params.ajax_url, // From WooCommerce
        data: {
          action: 'remove_selected_cart_items', // Your action name
          cart_item_keys: selectedItems
        },
        success: function(response) {
          window.location.reload();
        },
        error: function(jqXHR, textStatus, errorThrown) {
          console.log('Error deleting items:', textStatus, errorThrown);
          alert('Failed to delete selected items. Please try again.');
        }
      });
    }
  });
});
// This is the correct way to get the ajax_url.  Do not hardcode it.
var wc_ajax_params = { //Correct way to define wc_ajax_params
  'ajax_url': '<?php echo admin_url( 'admin-ajax.php' ); ?>'
};


document.getElementById('apply_coupon').addEventListener('click', function() {
  let couponCode = document.getElementById('coupon_code').value.trim();
  let messageBox = document.getElementById('coupon_message');

  if (!couponCode) {
    messageBox.textContent = "Please enter a coupon code.";
    messageBox.style.color = "red";
    return;
  }

  let data = new FormData();
  data.append('action', 'apply_coupon');
  data.append('coupon_code', couponCode);

  fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
      method: 'POST',
      body: data
    })
    .then(response => response.json())
    .then(result => {
      if (result.success) {
        messageBox.textContent = "Coupon applied successfully!";
        messageBox.style.color = "green";
        location.reload(); // Reload cart to reflect discount
      } else {
        messageBox.textContent = result.message;
        messageBox.style.color = "red";
      }
    })
    .catch(error => console.error('Error:', error));
});
</script>

<!-- Shipping Method Selection Modal -->
<!-- <div class="modal fade" id="shippingModal" tabindex="-1" aria-labelledby="shippingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="shippingModalLabel">
          <i data-feather="truck" class="feather-sm me-2"></i>
          <?php esc_html_e('Select Shipping Method', 'tendeal'); ?>
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="shipping-loading" class="text-center py-4" style="display: none;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden"><?php esc_html_e('Loading...', 'tendeal'); ?></span>
          </div>
          <p class="mt-2"><?php esc_html_e('Loading shipping methods...', 'tendeal'); ?></p>
        </div>

        <div id="shipping-content">
          <div class="vendor-shipping-info mb-4">
            <div class="d-flex align-items-center">
              <div class="vendor-logo me-3">
                <span id="vendor-initial"></span>
              </div>
              <div>
                <h6 class="mb-1" id="vendor-name"></h6>
                <small class="text-muted" id="vendor-location"></small>
              </div>
            </div>
          </div>

          <div class="shipping-methods">
            <h6 class="mb-3"><?php esc_html_e('Available Shipping Methods:', 'tendeal'); ?></h6>
            <div id="shipping-methods-list">
              <!-- Shipping methods will be loaded here --/////////
            </div>
          </div>

          <div class="delivery-estimate mt-4">
            <div class="alert alert-info">
              <i data-feather="info" class="feather-sm me-2"></i>
              <span id="delivery-estimate-text">
                <?php esc_html_e('Select a shipping method to see delivery estimate', 'tendeal'); ?>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <?php esc_html_e('Cancel', 'tendeal'); ?>
        </button>
        <button type="button" class="btn btn-primary" id="confirm-shipping-method" disabled>
          <?php esc_html_e('Confirm Selection', 'tendeal'); ?>
        </button>
      </div>
    </div>
  </div>
</div> -->

<!-- Saved Address Selection Modal -->
<div class="modal fade" id="savedAddressModal" tabindex="-1" aria-labelledby="savedAddressModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="savedAddressModalLabel">
          <i data-feather="map-pin" class="feather-sm me-2"></i>
          <?php esc_html_e('Select Delivery Address', 'tendeal'); ?>
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div id="address-loading" class="text-center py-4" style="display: none;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden"><?php esc_html_e('Loading...', 'tendeal'); ?></span>
          </div>
          <p class="mt-2"><?php esc_html_e('Loading saved addresses...', 'tendeal'); ?></p>
        </div>

        <div id="address-content">
          <div class="saved-addresses">
            <h6 class="mb-3"><?php esc_html_e('Your Saved Addresses:', 'tendeal'); ?></h6>
            <div id="saved-addresses-list">
              <!-- Saved addresses will be loaded here -->
            </div>
          </div>

          <div class="add-new-address mt-4">
            <button type="button" class="btn btn-outline-primary" id="add-new-address-btn">
              <i data-feather="plus" class="feather-sm me-2"></i>
              <?php esc_html_e('Add New Address', 'tendeal'); ?>
            </button>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <?php esc_html_e('Cancel', 'tendeal'); ?>
        </button>
        <button type="button" class="btn btn-primary" id="confirm-address-selection" disabled>
          <?php esc_html_e('Use This Address', 'tendeal'); ?>
        </button>
      </div>
    </div>
  </div>
</div>

<?php get_footer(); ?>