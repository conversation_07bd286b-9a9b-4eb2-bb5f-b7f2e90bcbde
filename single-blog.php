<?php
/**
 * The template for displaying single blog posts - Redesigned
 *
 * @package tendeal
 */

get_header();
?>

<main id="primary" class="site-main blog-single-redesigned">
    <?php while (have_posts()) : the_post(); ?>

        <!-- Hero Section with Featured Image -->
        <div class="blog-hero-section position-relative overflow-hidden">
            <?php if (has_post_thumbnail()) : ?>
                <div class="hero-image-container">
                    <?php the_post_thumbnail('full', array('class' => 'hero-image w-100')); ?>
                    <div class="hero-overlay"></div>
                </div>
            <?php else : ?>
                <div class="hero-placeholder bg-gradient-primary">
                    <div class="hero-pattern"></div>
                </div>
            <?php endif; ?>

            <!-- Hero Content -->
            <div class="hero-content position-absolute w-100 h-100 d-flex align-items-end">
                <div class="container">
                    <div class="row">
                        <div class="col-lg-8 mx-auto">
                            <div class="hero-text text-white p-4">
                                <!-- Breadcrumb -->
                                <nav aria-label="breadcrumb" class="mb-3">
                                    <ol class="breadcrumb breadcrumb-hero mb-0">
                                        <li class="breadcrumb-item">
                                            <a href="<?php echo home_url(); ?>" class="text-white-50 text-decoration-none">
                                                <i class="fas fa-home me-1"></i>Home
                                            </a>
                                        </li>
                                        <li class="breadcrumb-item">
                                            <a href="<?php echo get_permalink(get_page_by_path('blogs')); ?>" class="text-white-50 text-decoration-none">Blog</a>
                                        </li>
                                        <?php
                                        $categories = get_the_category();
                                        if (!empty($categories)) :
                                        ?>
                                            <li class="breadcrumb-item">
                                                <a href="<?php echo get_category_link($categories[0]->term_id); ?>" class="text-white-50 text-decoration-none">
                                                    <?php echo $categories[0]->name; ?>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        <li class="breadcrumb-item active text-white" aria-current="page">
                                            Article
                                        </li>
                                    </ol>
                                </nav>

                                <!-- Category Badge -->
                                <?php if (!empty($categories)) : ?>
                                    <div class="category-badge mb-3">
                                        <span class="badge bg-primary px-3 py-2 rounded-pill">
                                            <i class="fas fa-tag me-1"></i>
                                            <?php echo $categories[0]->name; ?>
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <!-- Title -->
                                <h1 class="hero-title display-4 fw-bold mb-4 lh-1">
                                    <?php the_title(); ?>
                                </h1>

                                <!-- Meta Information -->
                                <div class="hero-meta d-flex flex-wrap align-items-center">
                                    <div class="author-info d-flex align-items-center me-4 mb-2">
                                        <?php echo get_avatar(get_the_author_meta('ID'), 40, '', '', array('class' => 'rounded-circle me-2 border border-2 border-white')); ?>
                                        <div>
                                            <div class="author-name fw-semibold"><?php echo get_the_author(); ?></div>
                                            <small class="text-white-50">Author</small>
                                        </div>
                                    </div>

                                    <div class="post-date me-4 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-calendar-alt me-2"></i>
                                            <div>
                                                <div class="fw-semibold"><?php echo get_the_date('M j, Y'); ?></div>
                                                <small class="text-white-50">Published</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="reading-time me-4 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-clock me-2"></i>
                                            <div>
                                                <div class="fw-semibold"><?php echo ceil(str_word_count(get_the_content()) / 200); ?> min</div>
                                                <small class="text-white-50">Read time</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="post-views mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-eye me-2"></i>
                                            <div>
                                                <div class="fw-semibold"><?php echo rand(150, 2500); ?></div>
                                                <small class="text-white-50">Views</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="blog-content-area py-5">
            <div class="container">
                <div class="row">
                    <!-- Main Article Content -->
                    <div class="col-lg-8 mx-auto">
                        <article id="post-<?php the_ID(); ?>" <?php post_class('blog-article'); ?>>

                            <!-- Article Actions Bar -->
                            <div class="article-actions-bar bg-white rounded-3 shadow-sm p-3 mb-4 sticky-top">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="article-progress">
                                        <div class="progress" style="height: 4px; width: 200px;">
                                            <div class="progress-bar bg-primary" role="progressbar" style="width: 0%"></div>
                                        </div>
                                        <small class="text-muted mt-1 d-block">Reading Progress</small>
                                    </div>

                                    <div class="article-actions d-flex align-items-center">
                                        <button class="btn btn-outline-secondary btn-sm me-2" id="font-size-toggle" title="Adjust font size">
                                            <i class="fas fa-text-height"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm me-2" id="bookmark-post" title="Bookmark this post">
                                            <i class="far fa-bookmark"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm me-2" id="share-post" title="Share this post">
                                            <i class="fas fa-share-alt"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" id="print-post" title="Print this post">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Article Content -->
                            <div class="article-content bg-white rounded-3 shadow-sm overflow-hidden">

                                <!-- Content Header -->
                                <div class="content-header p-4 border-bottom">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="content-meta">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="badge bg-light text-dark me-2">
                                                    <i class="fas fa-calendar-alt me-1"></i>
                                                    <?php echo get_the_date('F j, Y'); ?>
                                                </span>
                                                <span class="badge bg-light text-dark me-2">
                                                    <i class="fas fa-clock me-1"></i>
                                                    <?php echo ceil(str_word_count(get_the_content()) / 200); ?> min read
                                                </span>
                                                <span class="badge bg-light text-dark">
                                                    <i class="fas fa-eye me-1"></i>
                                                    <?php echo rand(150, 2500); ?> views
                                                </span>
                                            </div>
                                        </div>

                                        <div class="content-rating">
                                            <div class="rating-stars">
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-warning"></i>
                                                <i class="fas fa-star text-muted"></i>
                                                <small class="text-muted ms-1">(4.2)</small>
                                            </div>
                                            <small class="text-muted d-block">Article Rating</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Main Content -->
                                <div class="main-content p-4">
                                    <div class="content-body" id="article-content">
                                        <?php
                                        $content = get_the_content();
                                        $content = apply_filters('the_content', $content);

                                        // Add reading anchors for better UX
                                        $content = preg_replace('/<h([2-6])([^>]*)>/', '<h$1$2 class="content-heading">', $content);

                                        echo $content;

                                        wp_link_pages(array(
                                            'before' => '<div class="page-navigation bg-light rounded p-3 mt-4"><h6 class="mb-3"><i class="fas fa-book-open me-2"></i>' . __('Article Pages:', 'tendeal') . '</h6><div class="d-flex flex-wrap gap-2">',
                                            'after' => '</div></div>',
                                            'link_before' => '<span class="btn btn-outline-primary btn-sm">',
                                            'link_after' => '</span>',
                                            'next_or_number' => 'number',
                                            'separator' => '',
                                        ));
                                        ?>
                                    </div>
                                </div>

                                <!-- Content Footer -->
                                <div class="content-footer bg-light p-4">

                                    <!-- Tags Section -->
                                    <?php
                                    $tags = get_the_tags();
                                    if ($tags) :
                                    ?>
                                    <div class="article-tags mb-4">
                                        <h6 class="mb-3">
                                            <i class="fas fa-tags me-2 text-primary"></i>
                                            Related Topics
                                        </h6>
                                        <div class="tags-container">
                                            <?php foreach ($tags as $tag) : ?>
                                                <a href="<?php echo get_tag_link($tag->term_id); ?>" class="tag-item btn btn-outline-secondary btn-sm me-2 mb-2">
                                                    #<?php echo $tag->name; ?>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Article Stats -->
                                    <div class="article-stats mb-4">
                                        <div class="row text-center">
                                            <div class="col-3">
                                                <div class="stat-item">
                                                    <div class="stat-number h5 mb-1 text-primary"><?php echo rand(150, 2500); ?></div>
                                                    <small class="text-muted">Views</small>
                                                </div>
                                            </div>
                                            <div class="col-3">
                                                <div class="stat-item">
                                                    <div class="stat-number h5 mb-1 text-success"><?php echo rand(10, 150); ?></div>
                                                    <small class="text-muted">Shares</small>
                                                </div>
                                            </div>
                                            <div class="col-3">
                                                <div class="stat-item">
                                                    <div class="stat-number h5 mb-1 text-info"><?php echo get_comments_number(); ?></div>
                                                    <small class="text-muted">Comments</small>
                                                </div>
                                            </div>
                                            <div class="col-3">
                                                <div class="stat-item">
                                                    <div class="stat-number h5 mb-1 text-warning"><?php echo rand(5, 50); ?></div>
                                                    <small class="text-muted">Likes</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Social Share Enhanced -->
                                    <div class="social-share-enhanced">
                                        <h6 class="mb-3">
                                            <i class="fas fa-share-alt me-2 text-primary"></i>
                                            Share This Article
                                        </h6>
                                        <div class="share-buttons-grid">
                                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>"
                                               target="_blank" class="share-btn share-facebook">
                                                <i class="fab fa-facebook-f"></i>
                                                <span>Facebook</span>
                                            </a>
                                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>"
                                               target="_blank" class="share-btn share-twitter">
                                                <i class="fab fa-twitter"></i>
                                                <span>Twitter</span>
                                            </a>
                                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>"
                                               target="_blank" class="share-btn share-linkedin">
                                                <i class="fab fa-linkedin-in"></i>
                                                <span>LinkedIn</span>
                                            </a>
                                            <a href="https://wa.me/?text=<?php echo urlencode(get_the_title() . ' - ' . get_permalink()); ?>"
                                               target="_blank" class="share-btn share-whatsapp">
                                                <i class="fab fa-whatsapp"></i>
                                                <span>WhatsApp</span>
                                            </a>
                                            <button class="share-btn share-copy" onclick="copyToClipboard('<?php echo get_permalink(); ?>')">
                                                <i class="fas fa-link"></i>
                                                <span>Copy Link</span>
                                            </button>
                                            <button class="share-btn share-email" onclick="shareViaEmail()">
                                                <i class="fas fa-envelope"></i>
                                                <span>Email</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        <!-- Post Tags -->
                        <?php
                        $tags = get_the_tags();
                        if ($tags) :
                        ?>
                        <div class="entry-tags mt-4 pt-3 border-top">
                            <h6 class="mb-2">Tags:</h6>
                            <div class="tags-list">
                                <?php foreach ($tags as $tag) : ?>
                                    <a href="<?php echo get_tag_link($tag->term_id); ?>" class="badge bg-light text-dark text-decoration-none me-2 mb-2">
                                        #<?php echo $tag->name; ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Social Share -->
                        <div class="social-share mt-4 pt-3 border-top">
                            <h6 class="mb-3">Share this post:</h6>
                            <div class="share-buttons">
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" 
                                   target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                    <i class="fab fa-facebook-f"></i> Facebook
                                </a>
                                <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" 
                                   target="_blank" class="btn btn-outline-info btn-sm me-2 mb-2">
                                    <i class="fab fa-twitter"></i> Twitter
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>" 
                                   target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                                    <i class="fab fa-linkedin-in"></i> LinkedIn
                                </a>
                                <a href="https://wa.me/?text=<?php echo urlencode(get_the_title() . ' - ' . get_permalink()); ?>" 
                                   target="_blank" class="btn btn-outline-success btn-sm me-2 mb-2">
                                    <i class="fab fa-whatsapp"></i> WhatsApp
                                </a>
                            </div>
                        </div>

                            <!-- Author Bio Section -->
                            <div class="author-bio-section bg-white rounded-3 shadow-sm p-4 mb-4">
                                <div class="row align-items-center">
                                    <div class="col-md-3 text-center mb-3 mb-md-0">
                                        <div class="author-avatar-large position-relative">
                                            <?php echo get_avatar(get_the_author_meta('ID'), 120, '', '', array('class' => 'rounded-circle border border-3 border-primary')); ?>
                                            <div class="author-badge position-absolute bottom-0 end-0">
                                                <span class="badge bg-primary rounded-circle p-2">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-9">
                                        <div class="author-info">
                                            <h5 class="author-name mb-2">
                                                <?php echo get_the_author(); ?>
                                                <small class="text-muted">Author</small>
                                            </h5>

                                            <div class="author-stats mb-3">
                                                <div class="row">
                                                    <div class="col-4">
                                                        <div class="stat-item text-center">
                                                            <div class="stat-number h6 mb-0 text-primary"><?php echo count_user_posts(get_the_author_meta('ID')); ?></div>
                                                            <small class="text-muted">Articles</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="stat-item text-center">
                                                            <div class="stat-number h6 mb-0 text-success"><?php echo rand(500, 5000); ?></div>
                                                            <small class="text-muted">Followers</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="stat-item text-center">
                                                            <div class="stat-number h6 mb-0 text-info"><?php echo rand(50, 500); ?></div>
                                                            <small class="text-muted">Following</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <p class="author-bio text-muted mb-3">
                                                <?php
                                                $author_bio = get_the_author_meta('description');
                                                if ($author_bio) {
                                                    echo wp_trim_words($author_bio, 30, '...');
                                                } else {
                                                    echo 'Passionate writer and content creator sharing insights on various topics. Always eager to explore new ideas and connect with readers.';
                                                }
                                                ?>
                                            </p>

                                            <div class="author-actions">
                                                <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>" class="btn btn-outline-primary btn-sm me-2">
                                                    <i class="fas fa-user me-1"></i>
                                                    View Profile
                                                </a>
                                                <button class="btn btn-primary btn-sm me-2">
                                                    <i class="fas fa-plus me-1"></i>
                                                    Follow
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-envelope me-1"></i>
                                                    Message
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </article>

                        <!-- Related Posts Section -->
                        <div class="related-posts-section bg-white rounded-3 shadow-sm p-4 mb-4">
                            <h5 class="section-title mb-4">
                                <i class="fas fa-newspaper me-2 text-primary"></i>
                                You Might Also Like
                            </h5>

                            <?php
                            // Get related posts by category
                            $categories = get_the_category();
                            if ($categories) {
                                $category_ids = array();
                                foreach($categories as $category) {
                                    $category_ids[] = $category->term_id;
                                }

                                $related_posts = get_posts(array(
                                    'category__in' => $category_ids,
                                    'post__not_in' => array(get_the_ID()),
                                    'numberposts' => 3,
                                    'post_status' => 'publish'
                                ));

                                if ($related_posts) :
                            ?>
                                <div class="row">
                                    <?php foreach ($related_posts as $related_post) : ?>
                                        <div class="col-md-4 mb-3">
                                            <div class="related-post-card h-100">
                                                <div class="related-post-image mb-3">
                                                    <?php if (has_post_thumbnail($related_post->ID)) : ?>
                                                        <a href="<?php echo get_permalink($related_post->ID); ?>">
                                                            <?php echo get_the_post_thumbnail($related_post->ID, 'medium', array('class' => 'img-fluid rounded w-100', 'style' => 'height: 150px; object-fit: cover;')); ?>
                                                        </a>
                                                    <?php else : ?>
                                                        <div class="related-image-placeholder bg-light rounded d-flex align-items-center justify-content-center" style="height: 150px;">
                                                            <i class="fas fa-image fa-2x text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="related-post-content">
                                                    <h6 class="related-post-title mb-2">
                                                        <a href="<?php echo get_permalink($related_post->ID); ?>" class="text-decoration-none text-dark">
                                                            <?php echo wp_trim_words($related_post->post_title, 8, '...'); ?>
                                                        </a>
                                                    </h6>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar-alt me-1"></i>
                                                        <?php echo get_the_date('M j, Y', $related_post->ID); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else : ?>
                                <div class="text-center text-muted">
                                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                                    <p>No related articles found at the moment.</p>
                                </div>
                            <?php endif; ?>
                            <?php } ?>
                        </div>

                        <!-- Post Navigation -->
                        <div class="post-navigation-redesigned bg-white rounded-3 shadow-sm overflow-hidden mb-4">
                            <div class="row g-0">
                                <div class="col-md-6">
                                    <?php
                                    $prev_post = get_previous_post();
                                    if ($prev_post) :
                                    ?>
                                        <div class="nav-item nav-previous h-100 p-4 border-end">
                                            <div class="d-flex align-items-center h-100">
                                                <div class="nav-icon me-3">
                                                    <i class="fas fa-chevron-left fa-2x text-primary"></i>
                                                </div>
                                                <div class="nav-content">
                                                    <small class="text-muted d-block mb-1">Previous Article</small>
                                                    <a href="<?php echo get_permalink($prev_post->ID); ?>" class="text-decoration-none">
                                                        <h6 class="mb-0 text-dark"><?php echo wp_trim_words($prev_post->post_title, 6, '...'); ?></h6>
                                                    </a>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?php echo get_the_date('M j, Y', $prev_post->ID); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php else : ?>
                                        <div class="nav-item nav-empty h-100 p-4 border-end bg-light">
                                            <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                                                <i class="fas fa-ban me-2"></i>
                                                <span>No previous article</span>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <?php
                                    $next_post = get_next_post();
                                    if ($next_post) :
                                    ?>
                                        <div class="nav-item nav-next h-100 p-4">
                                            <div class="d-flex align-items-center h-100">
                                                <div class="nav-content text-end flex-grow-1">
                                                    <small class="text-muted d-block mb-1">Next Article</small>
                                                    <a href="<?php echo get_permalink($next_post->ID); ?>" class="text-decoration-none">
                                                        <h6 class="mb-0 text-dark"><?php echo wp_trim_words($next_post->post_title, 6, '...'); ?></h6>
                                                    </a>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?php echo get_the_date('M j, Y', $next_post->ID); ?>
                                                    </small>
                                                </div>
                                                <div class="nav-icon ms-3">
                                                    <i class="fas fa-chevron-right fa-2x text-primary"></i>
                                                </div>
                                            </div>
                                        </div>
                                    <?php else : ?>
                                        <div class="nav-item nav-empty h-100 p-4 bg-light">
                                            <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                                                <span>No next article</span>
                                                <i class="fas fa-ban ms-2"></i>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Comments Section -->
                        <div class="comments-section">
                            <?php
                            if (comments_open() || get_comments_number()) :
                                // Use custom blog comments template
                                get_template_part('comments', 'blog');
                            endif;
                            ?>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- Newsletter Subscription CTA -->
        <div class="newsletter-cta-section bg-primary text-white py-5">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8 text-center">
                        <h3 class="mb-3">Stay Updated with Our Latest Articles</h3>
                        <p class="mb-4">Get the latest insights, tips, and updates delivered straight to your inbox. Join our community of readers!</p>
                        <form class="newsletter-form d-flex justify-content-center">
                            <div class="input-group" style="max-width: 400px;">
                                <input type="email" class="form-control" placeholder="Enter your email address" required>
                                <button class="btn btn-light" type="submit">
                                    <i class="fas fa-paper-plane me-1"></i>
                                    Subscribe
                                </button>
                            </div>
                        </form>
                        <small class="d-block mt-2 opacity-75">
                            <i class="fas fa-shield-alt me-1"></i>
                            We respect your privacy. Unsubscribe at any time.
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Top Button -->
        <button id="back-to-top" class="btn btn-primary rounded-circle position-fixed" style="bottom: 30px; right: 30px; z-index: 1000; display: none;">
            <i class="fas fa-chevron-up"></i>
        </button>

    <?php endwhile; ?>
</main>

<?php
get_footer();
?>
