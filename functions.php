<?php
/**
 * tendeal functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package tendeal
 */

if ( ! defined( '_S_VERSION' ) ) {
	// Replace the version number of the theme on each release.
	define( '_S_VERSION', '1.0.0' );
}

/**
 * Start session for non-logged in users
 */
function tendeal_start_session() {
    if (!session_id() && !is_admin()) {
        session_start();
    }
}
add_action('init', 'tendeal_start_session', 1);

/**
 * Sets up theme defaults and registers support for various WordPress features.
 *
 * Note that this function is hooked into the after_setup_theme hook, which
 * runs before the init hook. The init hook is too late for some features, such
 * as indicating support for post thumbnails.
 */
function tendeal_setup() {
	/*
		* Make theme available for translation.
		* Translations can be filed in the /languages/ directory.
		* If you're building a theme based on tendeal, use a find and replace
		* to change 'tendeal' to the name of your theme in all the template files.
		*/
	load_theme_textdomain( 'tendeal', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	/*
		* Let WordPress manage the document title.
		* By adding theme support, we declare that this theme does not use a
		* hard-coded <title> tag in the document head, and expect WordPress to
		* provide it for us.
		*/
	add_theme_support( 'title-tag' );

	/*
		* Enable support for Post Thumbnails on posts and pages.
		*
		* @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		*/
	add_theme_support( 'post-thumbnails' );

	// Register navigation menu locations
	register_nav_menus(
		array(
			'menu-1' => esc_html__( 'Primary', 'tendeal' ),
			'footer-important' => esc_html__( 'Footer Important Links', 'tendeal' ),
			'footer-others' => esc_html__( 'Footer Other Links', 'tendeal' ),
		)
	);

	/*
		* Switch default core markup for search form, comment form, and comments
		* to output valid HTML5.
		*/
	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Set up the WordPress core custom background feature.
	add_theme_support(
		'custom-background',
		apply_filters(
			'tendeal_custom_background_args',
			array(
				'default-color' => 'ffffff',
				'default-image' => '',
			)
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

	/**
	 * Add support for core custom logo.
	 *
	 * @link https://codex.wordpress.org/Theme_Logo
	 */
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 250,
			'width'       => 250,
			'flex-width'  => true,
			'flex-height' => true,
		)
	);
}
add_action( 'after_setup_theme', 'tendeal_setup' );

/**
 * Helper function to ensure proper product initialization in loops
 * Fixes the issue where the first element in product loops appears empty
 *
 * @param WC_Product|null $product Current product object
 * @param WP_Post|null $post Current post object
 * @return WC_Product|null Valid product object or null if invalid
 */
function tendeal_ensure_valid_product( $product = null, $post = null ) {
    // Use global post if none provided
    if ( empty( $post ) ) {
        global $post;
    }

    // Return early if no post data
    if ( empty( $post ) ) {
        return null;
    }

    // If we don't have a valid product object, try to create one
    if ( empty( $product ) || ! is_a( $product, 'WC_Product' ) ) {
        $product = wc_get_product( $post->ID );
    }

    // Final validation - ensure we have a valid, visible product
    if ( ! $product || ! is_a( $product, 'WC_Product' ) || ! $product->is_visible() ) {
        return null;
    }

    // Set the global product to ensure consistency
    $GLOBALS['product'] = $product;

    return $product;
}

/**
 * Hook to ensure product global is properly set during WooCommerce loops
 */
function tendeal_fix_product_loop_globals() {
    global $product, $post;

    // Only run during product loops
    if ( ! wc_get_loop_prop( 'is_shortcode' ) && ! is_shop() && ! is_product_category() && ! is_product_tag() ) {
        return;
    }

    // Ensure we have a valid product
    $product = tendeal_ensure_valid_product( $product, $post );
}
add_action( 'woocommerce_shop_loop', 'tendeal_fix_product_loop_globals', 5 );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function tendeal_content_width() {
	$GLOBALS['content_width'] = apply_filters( 'tendeal_content_width', 640 );
}
add_action( 'after_setup_theme', 'tendeal_content_width', 0 );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function tendeal_widgets_init() {
	register_sidebar(
		array(
			'name'          => esc_html__( 'Sidebar', 'tendeal' ),
			'id'            => 'sidebar-1',
			'description'   => esc_html__( 'Add widgets here.', 'tendeal' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action( 'widgets_init', 'tendeal_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function tendeal_scripts() {
	wp_enqueue_style( 'tendeal-style', get_stylesheet_uri(), array(), _S_VERSION );
	wp_enqueue_style( 'tendeal-main', get_stylesheet_directory_uri() . '/css/main.css' );
	wp_enqueue_style( 'bootstrap-icons',  'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css' );
	wp_enqueue_style( 'tendeal-header-exact', get_stylesheet_directory_uri() . '/css/header-exact.css', array(), _S_VERSION );
	wp_enqueue_style( 'tendeal-full-width-fix', get_stylesheet_directory_uri() . '/css/full-width-fix.css', array(), _S_VERSION );
	wp_enqueue_style( 'tendeal-category-dropdown', get_stylesheet_directory_uri() . '/css/category-dropdown.css', array(), _S_VERSION );

	// Enqueue responsive improvements
	wp_enqueue_style( 'tendeal-responsive-improvements', get_stylesheet_directory_uri() . '/css/responsive-improvements.css', array(), _S_VERSION );

	// Enqueue search fixes
	wp_enqueue_style( 'tendeal-search-fix', get_stylesheet_directory_uri() . '/css/search-fix.css', array(), _S_VERSION );

	// Enqueue shop improvements
	wp_enqueue_style( 'tendeal-shop-improvements', get_stylesheet_directory_uri() . '/css/shop-improvements.css', array(), _S_VERSION );

	// Enqueue clean shop header
	wp_enqueue_style( 'tendeal-shop-header-clean', get_stylesheet_directory_uri() . '/css/shop-header-clean.css', array(), _S_VERSION );

	// Enqueue mobile layout fix (high priority)
	wp_enqueue_style( 'tendeal-mobile-layout-fix', get_stylesheet_directory_uri() . '/css/mobile-layout-fix.css', array(), _S_VERSION );

	// Enqueue cart dropdown styles
	wp_enqueue_style( 'tendeal-cart-dropdown', get_stylesheet_directory_uri() . '/css/cart-dropdown.css', array(), _S_VERSION );

	// Enqueue updated product card styles
	wp_enqueue_style( 'tendeal-product-card-updated', get_stylesheet_directory_uri() . '/css/product-card-updated-styles.css', array(), _S_VERSION );

	// Enqueue Feather Icons
	wp_enqueue_script( 'feather-icons', 'https://unpkg.com/feather-icons', array(), null, true );
	wp_add_inline_script( 'feather-icons', 'document.addEventListener("DOMContentLoaded", function() { feather.replace(); });' );

	// Front page responsive styles
	if ( is_front_page() ) {
		wp_enqueue_style( 'tendeal-front-page-responsive', get_stylesheet_directory_uri() . '/css/front-page-responsive.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-todays-offers', get_stylesheet_directory_uri() . '/css/todays-offers.css', array(), _S_VERSION );
		wp_enqueue_script( 'tendeal-offer-countdown', get_template_directory_uri() . '/js/offer-countdown.js', array('jquery'), _S_VERSION, true );
	}

	// Account dashboard and pages styles
	if ( is_account_page() || (isset($_GET['action']) && $_GET['action'] === 'register') ) {
		wp_enqueue_style( 'tendeal-account-dashboard', get_stylesheet_directory_uri() . '/css/account-dashboard.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-account-pages', get_stylesheet_directory_uri() . '/css/account-pages.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-address-card', get_stylesheet_directory_uri() . '/css/address-card.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-orders-page', get_stylesheet_directory_uri() . '/css/orders-page.css', array(), _S_VERSION );
		wp_enqueue_script( 'tendeal-address-card', get_template_directory_uri() . '/js/address-card.js', array('jquery'), _S_VERSION, true );
	}

	// Modern shop page styles
	if ( is_page_template('shop-modern.php') ) {
		wp_enqueue_style( 'tendeal-shop-modern', get_stylesheet_directory_uri() . '/css/shop-modern.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-shop-notifications', get_stylesheet_directory_uri() . '/css/shop-notifications.css', array(), _S_VERSION );
	}

	// Shop page with brands styles
	if ( is_page_template('shop-brands.php') ) {
		wp_enqueue_style( 'tendeal-shop-brands', get_stylesheet_directory_uri() . '/css/shop-brands.css', array(), _S_VERSION );
	}

	// Shop page with filters styles
	if ( is_page_template('shop-filtered.php') ) {
		wp_enqueue_style( 'tendeal-shop-filtered', get_stylesheet_directory_uri() . '/css/shop-filtered.css', array(), _S_VERSION );
	}

	// Category page styles
	if ( is_page_template('category-page-template.php') ) {
		wp_enqueue_style( 'tendeal-category-page', get_stylesheet_directory_uri() . '/css/category-page.css', array(), _S_VERSION );
	}

	// Contact page styles
	if ( is_page_template('page-contact.php') ) {
		wp_enqueue_style( 'tendeal-contact-page', get_stylesheet_directory_uri() . '/css/contact-page.css', array(), _S_VERSION );
	}

	// Home Electronics template styles
	if ( is_page_template('home-electronics.php') ) {
		wp_enqueue_style( 'tendeal-home-electronics', get_stylesheet_directory_uri() . '/css/home-electronics.css', array(), _S_VERSION );
	}

	// Electronics Home template styles
	if ( is_page_template('electronics-home.php') ) {
		wp_enqueue_style( 'tendeal-electronics-home', get_stylesheet_directory_uri() . '/css/electronics-home.css', array(), _S_VERSION );
	}

	// Electronics Shop Home template styles
	if ( is_page_template('electronics-shop-home.php') || is_page_template('front-page.php') ) {
		wp_enqueue_style( 'tendeal-electronics-shop-home', get_stylesheet_directory_uri() . '/css/electronics-shop-home.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-offer-card', get_stylesheet_directory_uri() . '/css/offer-card.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-product-card-modern', get_stylesheet_directory_uri() . '/css/product-card-modern.css', array(), _S_VERSION );
	}

	// Shop with dynamic filters styles - apply to all product archive pages
	if ( is_page_template('shop-dynamic-filters.php') || is_shop() || is_product_category() || is_product_tag() ||
	     is_tax('product_brand') || (function_exists('is_product_taxonomy') && is_product_taxonomy()) ) {
		wp_enqueue_style( 'tendeal-shop-sidebar-updated', get_stylesheet_directory_uri() . '/css/shop-sidebar-updated.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-product-card-updated', get_stylesheet_directory_uri() . '/css/product-card-updated.css', array(), _S_VERSION );
	}
	// ROG Gaming Style Product Page - apply to single product pages
	if ( is_product() ) {
		wp_enqueue_style( 'tendeal-rog-product-page', get_stylesheet_directory_uri() . '/css/rog-product-page.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-share-modal', get_stylesheet_directory_uri() . '/css/share-modal.css', array(), _S_VERSION );
		// Ensure Bootstrap Icons are loaded
		wp_enqueue_style( 'bootstrap-icons', 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css', array(), null );
		// Ensure product configuration script is loaded
		wp_enqueue_script( 'tendeal-product-config', get_template_directory_uri() . '/js/product-config.js', array('jquery'), _S_VERSION, true );
	}

	// ROG Gaming Style Wishlist Page
	if ( function_exists('YITH_WCWL') && is_page( YITH_WCWL()->get_wishlist_page_id() ) ) {
		wp_enqueue_style( 'tendeal-rog-wishlist', get_stylesheet_directory_uri() . '/css/rog-wishlist.css', array(), _S_VERSION );
	}

	// Cart Page Styles
	if ( is_cart() ) {
		wp_enqueue_style( 'tendeal-empty-cart', get_stylesheet_directory_uri() . '/css/empty-cart.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-cart-vendor', get_stylesheet_directory_uri() . '/css/cart-vendor-style.css', array(), _S_VERSION );
	}

	// 404 Page Styles
	if ( is_404() ) {
		wp_enqueue_style( 'tendeal-404-page', get_stylesheet_directory_uri() . '/css/404-page.css', array(), _S_VERSION );
	}

	// Blog Pages Styles and Scripts
	if ( is_page_template('page-blogs.php') || is_single() || is_home() || is_category() || is_tag() || is_author() || is_date() ) {
		wp_enqueue_style( 'tendeal-blog-styles', get_stylesheet_directory_uri() . '/css/blog-styles.css', array(), _S_VERSION );
		wp_enqueue_script( 'tendeal-blog-functionality', get_template_directory_uri() . '/js/blog-functionality.js', array('jquery'), _S_VERSION, true );

		// Localize script for blog functionality
		wp_localize_script('tendeal-blog-functionality', 'blog_ajax', array(
			'ajax_url' => admin_url('admin-ajax.php'),
			'nonce' => wp_create_nonce('blog_ajax_nonce')
		));
	}

	// Checkout Page Styles
	if ( is_checkout() ) {
		wp_enqueue_style( 'tendeal-checkout', get_stylesheet_directory_uri() . '/css/checkout.css', array(), _S_VERSION );
		wp_enqueue_style( 'tendeal-address-card', get_stylesheet_directory_uri() . '/css/address-card.css', array(), _S_VERSION );
		wp_enqueue_script( 'tendeal-checkout', get_template_directory_uri() . '/js/checkout.js', array('jquery'), _S_VERSION, true );
		wp_enqueue_script( 'tendeal-address-card', get_template_directory_uri() . '/js/address-card.js', array('jquery'), _S_VERSION, true );
	}

	// Seller Categories Page
	if ( is_page_template('seller-page.php') ||  is_page_template('saller.php')  ) {
		wp_enqueue_style( 'tendeal-seller-categories', get_stylesheet_directory_uri() . '/css/rog-sellers.css', array(), _S_VERSION );
		// Ensure Bootstrap Icons are loaded
		wp_enqueue_style( 'bootstrap-icons', 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css', array(), null );
	}

	// WCFM Store Pages
	if ( function_exists('wcfmmp_is_store_page') && wcfmmp_is_store_page() || is_page_template('wcfm-store-template.php') ) {
		wp_enqueue_style( 'tendeal-wcfm-store-template', get_stylesheet_directory_uri() . '/css/wcfm-store-template.css', array(), _S_VERSION );
		// Ensure Bootstrap Icons are loaded
		wp_enqueue_style( 'bootstrap-icons', 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css', array(), null );
		// Add the JavaScript fix with a high priority to run after all other scripts
		wp_enqueue_script( 'tendeal-wcfm-store-fix', get_stylesheet_directory_uri() . '/js/wcfm-store-fix.js', array('jquery'), _S_VERSION, true );
	}

	// Bootstrap JS (with Popper.js, required for tabs)
	wp_enqueue_script('bootstrap-js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js', array('jquery'), null, true);

	// Header JS
	wp_enqueue_script('tendeal-header-exact', get_template_directory_uri() . '/js/header-exact.js', array('jquery'), _S_VERSION, true);

	// Category dropdown JS
	wp_enqueue_script('tendeal-category-dropdown', get_template_directory_uri() . '/js/category-dropdown.js', array('jquery'), _S_VERSION, true);

	// Responsive enhancements JS
	wp_enqueue_script('tendeal-responsive-enhancements', get_template_directory_uri() . '/js/responsive-enhancements.js', array('jquery'), _S_VERSION, true);

	// Mobile layout fix JS (high priority)
	wp_enqueue_script('tendeal-mobile-layout-fix', get_template_directory_uri() . '/js/mobile-layout-fix.js', array('jquery'), _S_VERSION, true);

	// Product card updated JS
	wp_enqueue_script('tendeal-product-card-updated', get_template_directory_uri() . '/js/product-card-updated.js', array('jquery'), _S_VERSION, true);

	// Cart dropdown JS
	wp_enqueue_script('tendeal-cart-dropdown', get_template_directory_uri() . '/js/cart-dropdown.js', array('jquery'), _S_VERSION, true);

	// Search functionality fixes
	wp_enqueue_script('tendeal-search-fix', get_template_directory_uri() . '/js/search-fix.js', array('jquery'), _S_VERSION, true);

	// Shop improvements
	wp_enqueue_script('tendeal-shop-improvements', get_template_directory_uri() . '/js/shop-improvements.js', array('jquery'), _S_VERSION, true);

	// Localize script for product card functionality
	wp_localize_script('tendeal-product-card-updated', 'wc_add_to_cart_params', array(
		'ajax_url' => admin_url('admin-ajax.php'),
		'wc_ajax_url' => WC_AJAX::get_endpoint('%%endpoint%%'),
		'i18n_view_cart' => esc_attr__('View cart', 'woocommerce'),
		'cart_url' => wc_get_cart_url(),
		'is_cart' => is_cart(),
		'cart_redirect_after_add' => get_option('woocommerce_cart_redirect_after_add'),
		'nonce' => wp_create_nonce('woocommerce-add-to-cart'),
		'compare_nonce' => wp_create_nonce('tendeal_compare_nonce'),
		'is_user_logged_in' => is_user_logged_in()
	));

	// Add compare functionality variables if YITH Compare is not available
	if (!class_exists('YITH_Woocompare')) {
		wp_localize_script('tendeal-product-card-updated', 'tendeal_compare_params', array(
			'ajax_url' => admin_url('admin-ajax.php'),
			'nonce' => wp_create_nonce('tendeal_compare_nonce'),
			'compare_page_url' => home_url('/compare/'), // You can create a compare page
			'max_products' => 4
		));
	}

	// Modern shop page JS
	if ( is_page_template('shop-modern.php') ) {
		wp_enqueue_script('tendeal-shop-modern', get_template_directory_uri() . '/js/shop-modern.js', array('jquery'), _S_VERSION, true);
	}

	// Shop page with brands JS
	if ( is_page_template('shop-brands.php') ) {
		wp_enqueue_script('tendeal-shop-brands', get_template_directory_uri() . '/js/shop-brands.js', array('jquery'), _S_VERSION, true);
	}

	// Shop page with filters JS
	if ( is_page_template('shop-filtered.php') ) {
		wp_enqueue_script('tendeal-shop-filtered', get_template_directory_uri() . '/js/shop-filtered.js', array('jquery'), _S_VERSION, true);
	}

	// Home Electronics template JS
	if ( is_page_template('home-electronics.php') ) {
		wp_enqueue_script('tendeal-home-electronics', get_template_directory_uri() . '/js/home-electronics.js', array('jquery'), _S_VERSION, true);
	}

	// Electronics Home template JS
	if ( is_page_template('electronics-home.php') ) {
		wp_enqueue_script('tendeal-electronics-home', get_template_directory_uri() . '/js/electronics-home.js', array('jquery'), _S_VERSION, true);
	}

	// Electronics Shop Home template JS
	if ( is_page_template('electronics-shop-home.php') ) {
		wp_enqueue_script('tendeal-electronics-shop-home', get_template_directory_uri() . '/js/electronics-shop-home.js', array('jquery'), _S_VERSION, true);
		wp_enqueue_script('tendeal-offer-countdown', get_template_directory_uri() . '/js/offer-countdown.js', array('jquery'), _S_VERSION, true);
		wp_enqueue_script('tendeal-product-card-modern', get_template_directory_uri() . '/js/product-card-modern.js', array('jquery'), _S_VERSION, true);

		// Localize the script with WooCommerce data
		wp_localize_script('tendeal-product-card-modern', 'wc_add_to_cart_params', array(
			'ajax_url' => admin_url('admin-ajax.php'),
			'wc_ajax_url' => WC_AJAX::get_endpoint('%%endpoint%%'),
			'i18n_view_cart' => esc_attr__('View cart', 'woocommerce'),
			'cart_url' => wc_get_cart_url(),
			'is_cart' => is_cart(),
			'cart_redirect_after_add' => get_option('woocommerce_cart_redirect_after_add')
		));
	}

	// Shop with dynamic filters JS - apply to all product archive pages
	if ( is_page_template('shop-dynamic-filters.php') || is_shop() || is_product_category() || is_product_tag() ||
	     is_tax('product_brand') || (function_exists('is_product_taxonomy') && is_product_taxonomy()) ) {
		wp_enqueue_script('jquery-ui-slider');
		wp_enqueue_script('tendeal-shop-dynamic-filters', get_template_directory_uri() . '/js/shop-dynamic-filters.js', array('jquery', 'jquery-ui-slider'), _S_VERSION, true);
	}

	// Featured page JS - apply to featured page template
	if ( is_page_template('featured.php') ) {
		wp_enqueue_script('tendeal-featured-page', get_template_directory_uri() . '/js/featured-page.js', array('jquery'), _S_VERSION, true);

		// Localize script with AJAX data
		wp_localize_script('tendeal-featured-page', 'ajax_object', array(
			'ajax_url' => admin_url('admin-ajax.php'),
			'nonce' => wp_create_nonce('featured_products_nonce')
		));
	}


	// ROG Gaming Style Product Page JS - apply to single product pages
	if ( is_product() ) {
		wp_enqueue_script('tendeal-rog-product-page', get_template_directory_uri() . '/js/rog-product-page.js', array('jquery'), _S_VERSION, true);
		wp_enqueue_script('tendeal-product-page', get_template_directory_uri() . '/js/product-page.js', array('jquery'), _S_VERSION, true);

		// Enqueue the enhanced product card script for compare functionality
		wp_enqueue_script('tendeal-product-card-updated', get_template_directory_uri() . '/js/product-card-updated.js', array('jquery'), _S_VERSION, true);

		// Localize the script with WooCommerce data
		wp_localize_script('tendeal-product-page', 'wc_add_to_cart_params', array(
			'ajax_url' => admin_url('admin-ajax.php'),
			'wc_ajax_url' => WC_AJAX::get_endpoint('%%endpoint%%'),
			'i18n_view_cart' => esc_attr__('View cart', 'woocommerce'),
			'cart_url' => wc_get_cart_url(),
			'checkout_url' => wc_get_checkout_url(),
			'is_cart' => is_cart(),
			'cart_redirect_after_add' => get_option('woocommerce_cart_redirect_after_add'),
			'nonce' => wp_create_nonce('woocommerce-add-to-cart'),
			'compare_nonce' => wp_create_nonce('tendeal_compare_nonce')
		));

		// Also localize for the product card script
		wp_localize_script('tendeal-product-card-updated', 'wc_add_to_cart_params', array(
			'ajax_url' => admin_url('admin-ajax.php'),
			'wc_ajax_url' => WC_AJAX::get_endpoint('%%endpoint%%'),
			'i18n_view_cart' => esc_attr__('View cart', 'woocommerce'),
			'cart_url' => wc_get_cart_url(),
			'checkout_url' => wc_get_checkout_url(),
			'is_cart' => is_cart(),
			'cart_redirect_after_add' => get_option('woocommerce_cart_redirect_after_add'),
			'nonce' => wp_create_nonce('woocommerce-add-to-cart'),
			'compare_nonce' => wp_create_nonce('tendeal_compare_nonce')
		));
	}


	wp_style_add_data( 'tendeal-style', 'rtl', 'replace' );

	wp_enqueue_script( 'tendeal-navigation', get_template_directory_uri() . '/js/navigation.js', array(), _S_VERSION, true );
	wp_enqueue_style( 'bootstrap-popper',  'https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js', array('jquery') );
	wp_enqueue_style( 'bootstrap-script',  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js', array('jquery') );
	wp_enqueue_style( 'tendeal-script', get_stylesheet_directory_uri() . '/js/script.js', array('jquery') );

	wp_enqueue_script('store-products', get_template_directory_uri() . '/js/store-products.js', array('jquery'), null, true);
	// wp_localize_script('store-products', 'ajaxurl', admin_url('admin-ajax.php')); // Send AJAX URL to JS
	wp_localize_script('store-products', 'rfq_ajax', array(
		'ajaxurl' => admin_url('admin-ajax.php'),

));


	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}
}
add_action( 'wp_enqueue_scripts', 'tendeal_scripts' );

/**
 * Fix search functionality for products
 * Ensure search includes products and works properly with AWS plugin
 */
function tendeal_fix_search_functionality($query) {
    // Only modify main query on frontend
    if (is_admin() || !$query->is_main_query()) {
        return;
    }

    // Handle search queries
    if ($query->is_search()) {
        // If post_type is set to product, ensure it's a product search
        if (isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
            $query->set('post_type', 'product');
            $query->set('wc_query', 'product_query');

            // Ensure only visible products are shown
            $query->set('meta_query', array(
                array(
                    'key' => '_visibility',
                    'value' => array('catalog', 'visible'),
                    'compare' => 'IN'
                )
            ));
        } else {
            // Default search includes products
            $query->set('post_type', array('post', 'page', 'product'));
        }

        // Ensure published products only
        $query->set('post_status', 'publish');
    }
}
add_action('pre_get_posts', 'tendeal_fix_search_functionality');

/**
 * Ensure AWS search plugin works correctly
 */
function tendeal_aws_search_fix() {
    // Check if AWS plugin is active
    if (function_exists('aws_get_search_form')) {
        // Ensure AWS search includes all necessary post types
        add_filter('aws_search_post_types', function($post_types) {
            if (!in_array('product', $post_types)) {
                $post_types[] = 'product';
            }
            return $post_types;
        });

        // Ensure AWS search works with WooCommerce
        add_filter('aws_search_tax', function($taxonomies) {
            $taxonomies[] = 'product_cat';
            $taxonomies[] = 'product_tag';
            $taxonomies[] = 'product_brand';
            return $taxonomies;
        });
    }
}
add_action('init', 'tendeal_aws_search_fix');

/**
 * Debug search functionality
 */
function tendeal_debug_search() {
    if (is_search() && current_user_can('administrator')) {
        error_log('Search Query: ' . get_search_query());
        error_log('Post Type: ' . (isset($_GET['post_type']) ? $_GET['post_type'] : 'not set'));
        global $wp_query;
        error_log('WP Query post type: ' . print_r($wp_query->get('post_type'), true));
        error_log('Found posts: ' . $wp_query->found_posts);
    }
}
add_action('wp', 'tendeal_debug_search');

/**
 * Ensure search works with WooCommerce products
 */
function tendeal_include_products_in_search($query) {
    if (!is_admin() && $query->is_main_query()) {
        if ($query->is_search()) {
            // Include products in search results
            $post_types = $query->get('post_type');
            if (empty($post_types)) {
                $query->set('post_type', array('post', 'page', 'product'));
            }
        }
    }
}
add_action('pre_get_posts', 'tendeal_include_products_in_search');

/**
 * Fix search query for better product results
 */
function tendeal_search_query_fix($query) {
    if (!is_admin() && $query->is_search() && $query->is_main_query()) {
        // Ensure we're searching in product content and meta
        $search_term = $query->get('s');
        if (!empty($search_term)) {
            // Add meta query for product attributes
            $meta_query = array(
                'relation' => 'OR',
                array(
                    'key' => '_sku',
                    'value' => $search_term,
                    'compare' => 'LIKE'
                ),
                array(
                    'key' => '_product_attributes',
                    'value' => $search_term,
                    'compare' => 'LIKE'
                )
            );

            $existing_meta_query = $query->get('meta_query');
            if (!empty($existing_meta_query)) {
                $meta_query = array(
                    'relation' => 'OR',
                    $existing_meta_query,
                    $meta_query
                );
            }

            $query->set('meta_query', $meta_query);
        }
    }
}
add_action('pre_get_posts', 'tendeal_search_query_fix');

/**
 * Add search form shortcode for easy placement
 */
function tendeal_search_form_shortcode($atts) {
    $atts = shortcode_atts(array(
        'placeholder' => 'Search products...',
        'type' => 'product'
    ), $atts);

    ob_start();
    ?>
<form role="search" method="get" class="tendeal-search-form" action="<?php echo esc_url(home_url('/')); ?>">
  <div class="search-form-wrapper">
    <input type="search" class="search-field" placeholder="<?php echo esc_attr($atts['placeholder']); ?>"
      value="<?php echo get_search_query(); ?>" name="s" />
    <?php if ($atts['type'] === 'product') : ?>
    <input type="hidden" name="post_type" value="product" />
    <?php endif; ?>
    <button type="submit" class="search-submit">
      <i data-feather="search"></i>
    </button>
  </div>
</form>
<?php
    return ob_get_clean();
}
add_shortcode('tendeal_search', 'tendeal_search_form_shortcode');

/**
 * Redirect product searches to use product search template
 */
function tendeal_redirect_product_search_to_shop($template) {
    // Check if this is a search for products
    if (is_search() && (isset($_GET['post_type']) && $_GET['post_type'] === 'product')) {
        // Try to use the dedicated product search template
        $product_search_template = locate_template('search-product.php');
        if (!empty($product_search_template)) {
            return $product_search_template;
        }

        // Fallback to shop template for product searches
        $shop_template = locate_template('shop-dynamic-filters.php');
        if (!empty($shop_template)) {
            return $shop_template;
        }

        // Fallback to WooCommerce archive template
        $archive_template = locate_template('woocommerce/archive-product.php');
        if (!empty($archive_template)) {
            return $archive_template;
        }
    }

    return $template;
}
add_filter('template_include', 'tendeal_redirect_product_search_to_shop', 99);

/**
 * Force AWS search to search products by default
 */
function tendeal_force_aws_product_search() {
    if (function_exists('aws_get_search_form')) {
        // Add filter to modify AWS search form
        add_filter('aws_search_form_filters', function($filters) {
            $filters['post_type'] = 'product';
            return $filters;
        });

        // Ensure AWS searches products by default
        add_filter('aws_search_query_array', function($query_array) {
            if (!isset($query_array['post_type'])) {
                $query_array['post_type'] = 'product';
            }
            return $query_array;
        });
    }
}
add_action('init', 'tendeal_force_aws_product_search');

/**
 * Modify search results to show products properly
 */
function tendeal_modify_search_results($query) {
    if (!is_admin() && $query->is_main_query() && is_search()) {
        // If we're searching and no specific post type is set, prioritize products
        if (!isset($_GET['post_type'])) {
            // Check if search term matches any products
            $product_search = new WP_Query(array(
                'post_type' => 'product',
                's' => $query->get('s'),
                'posts_per_page' => 1,
                'fields' => 'ids'
            ));

            // If products are found, redirect to product search
            if ($product_search->have_posts()) {
                $search_url = add_query_arg(array(
                    's' => $query->get('s'),
                    'post_type' => 'product'
                ), home_url('/'));

                // Only redirect if we're not already on a product search
                if (!isset($_GET['post_type'])) {
                    wp_redirect($search_url);
                    exit;
                }
            }
        }
    }
}
add_action('pre_get_posts', 'tendeal_modify_search_results', 5);

/**
 * Enqueue footer newsletter script
 */
function tendeal_enqueue_footer_scripts() {
    // Only enqueue if not in admin
    if (!is_admin()) {
        wp_enqueue_script('tendeal-footer-newsletter', get_template_directory_uri() . '/js/footer-newsletter.js', array('jquery'), _S_VERSION, true);

        // Pass AJAX URL and nonce to script
        wp_localize_script('tendeal-footer-newsletter', 'tendeal_newsletter', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('tendeal_newsletter_nonce')
        ));
    }
}
add_action('wp_enqueue_scripts', 'tendeal_enqueue_footer_scripts');

/**
 * Handle checkout form processing for saved addresses
 */
function tendeal_handle_checkout_saved_address($order_id) {
    if (isset($_POST['selected_address_id']) && $_POST['selected_address_id'] === 'billing') {
        // If billing address is selected, the form fields should already be populated
        // This is handled by WooCommerce's default behavior
        return;
    }
}
add_action('woocommerce_checkout_create_order', 'tendeal_handle_checkout_saved_address');

/**
 * Pre-populate checkout fields with saved address data
 */
function tendeal_populate_checkout_fields($checkout) {
    if (is_user_logged_in() && isset($_POST['selected_address_id']) && $_POST['selected_address_id'] === 'billing') {
        $customer_id = get_current_user_id();

        // Get saved billing address data
        $billing_fields = array(
            'billing_first_name' => get_user_meta($customer_id, 'billing_first_name', true),
            'billing_last_name' => get_user_meta($customer_id, 'billing_last_name', true),
            'billing_company' => get_user_meta($customer_id, 'billing_company', true),
            'billing_address_1' => get_user_meta($customer_id, 'billing_address_1', true),
            'billing_address_2' => get_user_meta($customer_id, 'billing_address_2', true),
            'billing_city' => get_user_meta($customer_id, 'billing_city', true),
            'billing_state' => get_user_meta($customer_id, 'billing_state', true),
            'billing_postcode' => get_user_meta($customer_id, 'billing_postcode', true),
            'billing_country' => get_user_meta($customer_id, 'billing_country', true),
            'billing_email' => get_user_meta($customer_id, 'billing_email', true),
            'billing_phone' => get_user_meta($customer_id, 'billing_phone', true),
        );

        // Set the values in the checkout object
        foreach ($billing_fields as $key => $value) {
            if (!empty($value)) {
                $_POST[$key] = $value;
            }
        }
    }
}
add_action('woocommerce_checkout_init', 'tendeal_populate_checkout_fields');

/**
 * Handle newsletter subscription AJAX request
 */
function tendeal_newsletter_subscribe() {
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'tendeal_newsletter_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        wp_die();
    }

    // Get email
    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';

    // Validate email
    if (empty($email) || !is_email($email)) {
        wp_send_json_error(array('message' => 'Please enter a valid email address'));
        wp_die();
    }

    // Here you would typically add the email to your newsletter service
    // This is a placeholder for integration with services like Mailchimp, Constant Contact, etc.

    // For demonstration, we'll just store the email in WordPress options
    $subscribers = get_option('tendeal_newsletter_subscribers', array());

    // Check if already subscribed
    if (in_array($email, $subscribers)) {
        wp_send_json_success(array('message' => 'You are already subscribed!'));
        wp_die();
    }

    // Add to subscribers list
    $subscribers[] = $email;
    update_option('tendeal_newsletter_subscribers', $subscribers);

    // Send success response
    wp_send_json_success(array('message' => 'Thank you for subscribing!'));
    wp_die();
}
add_action('wp_ajax_tendeal_newsletter_subscribe', 'tendeal_newsletter_subscribe');
add_action('wp_ajax_nopriv_tendeal_newsletter_subscribe', 'tendeal_newsletter_subscribe');

/**
 * Handle Add to RFQ AJAX request
 */
function tendeal_add_to_rfq() {
    // Get product ID, quantity, and variation ID
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    $variation_id = isset($_POST['variation_id']) ? intval($_POST['variation_id']) : 0;

    // Validate product ID
    if (empty($product_id)) {
        wp_send_json_error(array('message' => 'Invalid product ID'));
        wp_die();
    }

    // Check if product exists
    $product = wc_get_product($product_id);
    if (!$product) {
        wp_send_json_error(array('message' => 'Product not found'));
        wp_die();
    }

    // Get current user ID
    $user_id = get_current_user_id();

    // Get RFQ items from user meta or session
    if ($user_id) {
        $rfq_items = get_user_meta($user_id, 'tendeal_rfq_items', true);
    } else {
        $rfq_items = isset($_SESSION['tendeal_rfq_items']) ? $_SESSION['tendeal_rfq_items'] : array();
    }

    // Initialize if empty
    if (!is_array($rfq_items)) {
        $rfq_items = array();
    }

    // Create a unique key for the product (including variation if applicable)
    $item_key = $variation_id > 0 ? $product_id . '_' . $variation_id : $product_id;

    // Add or update product in RFQ items
    $rfq_items[$item_key] = array(
        'product_id' => $product_id,
        'variation_id' => $variation_id,
        'quantity' => $quantity,
        'date_added' => current_time('mysql')
    );

    // Save RFQ items
    if ($user_id) {
        update_user_meta($user_id, 'tendeal_rfq_items', $rfq_items);
    } else {
        $_SESSION['tendeal_rfq_items'] = $rfq_items;
    }

    // Send success response
    wp_send_json_success(array(
        'message' => 'Product added to RFQ',
        'rfq_count' => count($rfq_items)
    ));
    wp_die();
}
add_action('wp_ajax_add_to_rfq', 'tendeal_add_to_rfq');
add_action('wp_ajax_nopriv_add_to_rfq', 'tendeal_add_to_rfq');

/**
 * Handle Compare functionality - Custom implementation with fallback to YITH
 */
function tendeal_add_to_compare() {
    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'tendeal_compare_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        wp_die();
    }

    // Get product ID
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $product_id = isset($_POST['id']) ? intval($_POST['id']) : $product_id; // Fallback for YITH format

    // Validate product ID
    if (empty($product_id)) {
        wp_send_json_error(array('message' => 'Invalid product ID'));
        wp_die();
    }

    // Check if product exists
    $product = wc_get_product($product_id);
    if (!$product) {
        wp_send_json_error(array('message' => 'Product not found'));
        wp_die();
    }

    // Try YITH Compare first if available
    if (class_exists('YITH_Woocompare') && function_exists('yith_woocompare_add_product_to_compare')) {
        $result = yith_woocompare_add_product_to_compare($product_id);
        if ($result) {
            wp_send_json_success(array(
                'message' => 'Product added to compare!',
                'result' => 'success'
            ));
        } else {
            wp_send_json_success(array(
                'message' => 'Product already in compare list',
                'result' => 'exists'
            ));
        }
        wp_die();
    }

    // Custom compare implementation as fallback
    $user_id = get_current_user_id();

    // Get compare items from user meta or session
    if ($user_id) {
        $compare_items = get_user_meta($user_id, 'tendeal_compare_items', true);
    } else {
        if (!session_id()) {
            session_start();
        }
        $compare_items = isset($_SESSION['tendeal_compare_items']) ? $_SESSION['tendeal_compare_items'] : array();
    }

    // Initialize if empty
    if (!is_array($compare_items)) {
        $compare_items = array();
    }

    // Check if product already in compare list
    if (in_array($product_id, $compare_items)) {
        wp_send_json_success(array(
            'message' => 'Product already in compare list',
            'result' => 'exists'
        ));
        wp_die();
    }

    // Limit compare items to 4 products
    if (count($compare_items) >= 4) {
        wp_send_json_error(array('message' => 'Compare list is full. Maximum 4 products allowed.'));
        wp_die();
    }

    // Add product to compare list
    $compare_items[] = $product_id;

    // Save compare items
    if ($user_id) {
        update_user_meta($user_id, 'tendeal_compare_items', $compare_items);
    } else {
        $_SESSION['tendeal_compare_items'] = $compare_items;
    }

    // Send success response
    wp_send_json_success(array(
        'message' => 'Product added to compare!',
        'result' => 'success',
        'compare_count' => count($compare_items)
    ));
    wp_die();
}
add_action('wp_ajax_tendeal_add_to_compare', 'tendeal_add_to_compare');
add_action('wp_ajax_nopriv_tendeal_add_to_compare', 'tendeal_add_to_compare');

// Also handle YITH Compare action as fallback
add_action('wp_ajax_yith_woocompare_add_product', 'tendeal_add_to_compare');
add_action('wp_ajax_nopriv_yith_woocompare_add_product', 'tendeal_add_to_compare');

/**
 * Handle Remove from Compare functionality
 */
function tendeal_remove_from_compare() {
    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'tendeal_compare_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        wp_die();
    }

    // Get product ID
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

    // Validate product ID
    if (empty($product_id)) {
        wp_send_json_error(array('message' => 'Invalid product ID'));
        wp_die();
    }

    // Get current user ID
    $user_id = get_current_user_id();

    // Get compare items from user meta or session
    if ($user_id) {
        $compare_items = get_user_meta($user_id, 'tendeal_compare_items', true);
    } else {
        if (!session_id()) {
            session_start();
        }
        $compare_items = isset($_SESSION['tendeal_compare_items']) ? $_SESSION['tendeal_compare_items'] : array();
    }

    // Initialize if empty
    if (!is_array($compare_items)) {
        $compare_items = array();
    }

    // Remove product from compare list
    $key = array_search($product_id, $compare_items);
    if ($key !== false) {
        unset($compare_items[$key]);
        $compare_items = array_values($compare_items); // Re-index array
    }

    // Save compare items
    if ($user_id) {
        update_user_meta($user_id, 'tendeal_compare_items', $compare_items);
    } else {
        $_SESSION['tendeal_compare_items'] = $compare_items;
    }

    // Send success response
    wp_send_json_success(array(
        'message' => 'Product removed from compare',
        'compare_count' => count($compare_items)
    ));
    wp_die();
}
add_action('wp_ajax_tendeal_remove_from_compare', 'tendeal_remove_from_compare');
add_action('wp_ajax_nopriv_tendeal_remove_from_compare', 'tendeal_remove_from_compare');

/**
 * Handle Clear All Compare functionality
 */
function tendeal_clear_compare() {
    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'tendeal_compare_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        wp_die();
    }

    // Get current user ID
    $user_id = get_current_user_id();

    // Clear compare items
    if ($user_id) {
        delete_user_meta($user_id, 'tendeal_compare_items');
    } else {
        if (!session_id()) {
            session_start();
        }
        unset($_SESSION['tendeal_compare_items']);
    }

    // Send success response
    wp_send_json_success(array(
        'message' => 'All products removed from compare',
        'compare_count' => 0
    ));
    wp_die();
}
add_action('wp_ajax_tendeal_clear_compare', 'tendeal_clear_compare');
add_action('wp_ajax_nopriv_tendeal_clear_compare', 'tendeal_clear_compare');

/**
 * Add debug information for compare functionality
 */
function tendeal_compare_debug_info() {
    if (current_user_can('administrator') && isset($_GET['compare_debug'])) {
        echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h3>Compare Debug Information</h3>';

        // Check if YITH Compare is active
        echo '<p><strong>YITH Compare Plugin:</strong> ';
        if (class_exists('YITH_Woocompare')) {
            echo '<span style="color: green;">✓ Active</span>';
        } else {
            echo '<span style="color: red;">✗ Not Active</span>';
        }
        echo '</p>';

        // Check compare items
        $user_id = get_current_user_id();
        if ($user_id) {
            $compare_items = get_user_meta($user_id, 'tendeal_compare_items', true);
            echo '<p><strong>User Compare Items:</strong> ' . (is_array($compare_items) ? count($compare_items) : 0) . ' items</p>';
            if (is_array($compare_items) && !empty($compare_items)) {
                echo '<ul>';
                foreach ($compare_items as $item) {
                    $product = wc_get_product($item);
                    echo '<li>' . ($product ? $product->get_name() : 'Product ID: ' . $item) . '</li>';
                }
                echo '</ul>';
            }
        } else {
            if (session_id()) {
                $compare_items = isset($_SESSION['tendeal_compare_items']) ? $_SESSION['tendeal_compare_items'] : array();
                echo '<p><strong>Session Compare Items:</strong> ' . (is_array($compare_items) ? count($compare_items) : 0) . ' items</p>';
            } else {
                echo '<p><strong>Session:</strong> Not started</p>';
            }
        }

        // Check JavaScript variables
        echo '<script>';
        echo 'console.log("Compare Debug - wc_add_to_cart_params:", typeof wc_add_to_cart_params !== "undefined" ? wc_add_to_cart_params : "undefined");';
        echo 'console.log("Compare Debug - yith_woocompare:", typeof yith_woocompare !== "undefined" ? yith_woocompare : "undefined");';
        echo 'console.log("Compare Debug - yith_woocompare_obj:", typeof yith_woocompare_obj !== "undefined" ? yith_woocompare_obj : "undefined");';
        echo '</script>';

        echo '</div>';
    }
}
add_action('wp_footer', 'tendeal_compare_debug_info');

/**
 * Handle AJAX Add to Cart
 */
function tendeal_ajax_add_to_cart() {
    // Verify nonce
    if (isset($_POST['security']) && !wp_verify_nonce($_POST['security'], 'woocommerce-add-to-cart')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        wp_die();
    }

    // Get product ID and quantity
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    $variation_id = isset($_POST['variation_id']) ? intval($_POST['variation_id']) : 0;

    // Validate product ID
    if (empty($product_id)) {
        wp_send_json_error(array('message' => 'Invalid product ID'));
        wp_die();
    }

    // Add to cart
    $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id);

    if ($cart_item_key) {
        // Get cart contents count
        $cart_count = WC()->cart->get_cart_contents_count();

        // Send success response
        wp_send_json_success(array(
            'message' => 'Product added to cart',
            'cart_count' => $cart_count,
            'cart_item_key' => $cart_item_key
        ));
    } else {
        // Send error response
        wp_send_json_error(array('message' => 'Failed to add product to cart'));
    }

    wp_die();
}
add_action('wp_ajax_woocommerce_ajax_add_to_cart', 'tendeal_ajax_add_to_cart');
add_action('wp_ajax_nopriv_woocommerce_ajax_add_to_cart', 'tendeal_ajax_add_to_cart');

/**
 * Handle AJAX request for loading featured products
 */
function tendeal_load_featured_products() {
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'featured_products_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        wp_die();
    }

    // Get product type and category
    $product_type = isset($_POST['product_type']) ? sanitize_text_field($_POST['product_type']) : 'featured';
    $category = isset($_POST['category']) ? sanitize_text_field($_POST['category']) : 'all';

    // Set up query arguments based on product type
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => 8,
        'post_status' => 'publish',
    );

    // Add category filter if not "all"
    if ($category !== 'all') {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'product_cat',
                'field'    => 'slug',
                'terms'    => $category,
            ),
        );
    }

    $title = '';

    switch ($product_type) {
        case 'featured':
            // Combine meta query with existing tax query if needed
            if (isset($args['tax_query'])) {
                $args['meta_query'] = array(
                    array(
                        'key' => '_featured',
                        'value' => 'yes',
                    ),
                );
            } else {
                $args['meta_key'] = '_featured';
                $args['meta_value'] = 'yes';
            }
            $title = 'Featured Products';
            break;

        case 'discounted':
            $meta_query = array(
                'relation' => 'AND',
                array(
                    'key' => '_sale_price',
                    'value' => '',
                    'compare' => '!='
                ),
                array(
                    'key' => '_regular_price',
                    'value' => '',
                    'compare' => '!='
                )
            );

            if (isset($args['tax_query'])) {
                $args['meta_query'] = $meta_query;
            } else {
                $args['meta_query'] = $meta_query;
            }
            $title = 'Discounted Products';
            break;

        case 'upcoming':
            $args['orderby'] = 'date';
            $args['order'] = 'DESC';
            $args['date_query'] = array(
                array(
                    'after' => '30 days ago',
                    'inclusive' => true,
                ),
            );
            $title = 'New & Upcoming Products';
            break;

        case 'popular':
            if (!isset($args['tax_query'])) {
                $args['meta_key'] = 'total_sales';
                $args['orderby'] = 'meta_value_num';
            } else {
                $args['meta_query'] = array(
                    array(
                        'key' => 'total_sales',
                        'value' => 0,
                        'compare' => '>',
                        'type' => 'NUMERIC'
                    ),
                );
                $args['orderby'] = 'meta_value_num';
                $args['meta_key'] = 'total_sales';
            }
            $args['order'] = 'DESC';
            $title = 'Popular Products';
            break;

        default:
            if (isset($args['tax_query'])) {
                $args['meta_query'] = array(
                    array(
                        'key' => '_featured',
                        'value' => 'yes',
                    ),
                );
            } else {
                $args['meta_key'] = '_featured';
                $args['meta_value'] = 'yes';
            }
            $title = 'Featured Products';
            break;
    }

    // Update title with category if specified
    if ($category !== 'all') {
        $category_term = get_term_by('slug', $category, 'product_cat');
        if ($category_term) {
            $title .= ' - ' . $category_term->name;
        }
    }

    // Execute query
    $products_query = new WP_Query($args);

    // If no products found for specific type, fallback to recent products
    if (!$products_query->have_posts() && $product_type !== 'featured') {
        $args = array(
            'post_type' => 'product',
            'posts_per_page' => 8,
            'post_status' => 'publish',
            'orderby' => 'date',
            'order' => 'DESC',
        );
        $products_query = new WP_Query($args);
        $title = 'Recent Products';
    }

    // Generate products HTML
    ob_start();

    if ($products_query->have_posts()) {
        while ($products_query->have_posts()) {
            $products_query->the_post();
            global $product;

            if (!is_a($product, 'WC_Product')) {
                continue;
            }

            echo '<div class="col-lg-3 col-md-4 col-sm-6 mb-4">';
            wc_get_template_part('content', 'product');
            echo '</div>';
        }
        wp_reset_postdata();
    } else {
        echo '<div class="col-12"><p class="text-center">No products found for this category.</p></div>';
    }

    $products_html = ob_get_clean();

    // Send success response
    wp_send_json_success(array(
        'title' => $title,
        'products' => $products_html
    ));

    wp_die();
}
add_action('wp_ajax_load_featured_products', 'tendeal_load_featured_products');
add_action('wp_ajax_nopriv_load_featured_products', 'tendeal_load_featured_products');

/**
 * Handle AJAX request for loading categories based on product type
 */
function tendeal_load_featured_categories() {
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'featured_products_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        wp_die();
    }

    // Get product type
    $product_type = isset($_POST['product_type']) ? sanitize_text_field($_POST['product_type']) : 'featured';

    // Get categories that have products of the specified type
    $categories_with_products = array();

    // Base query for getting product IDs
    $base_args = array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'fields' => 'ids',
    );

    // Add specific filters based on product type
    switch ($product_type) {
        case 'featured':
            $base_args['meta_key'] = '_featured';
            $base_args['meta_value'] = 'yes';
            break;

        case 'discounted':
            $base_args['meta_query'] = array(
                'relation' => 'AND',
                array(
                    'key' => '_sale_price',
                    'value' => '',
                    'compare' => '!='
                ),
                array(
                    'key' => '_regular_price',
                    'value' => '',
                    'compare' => '!='
                )
            );
            break;

        case 'upcoming':
            $base_args['orderby'] = 'date';
            $base_args['order'] = 'DESC';
            $base_args['date_query'] = array(
                array(
                    'after' => '30 days ago',
                    'inclusive' => true,
                ),
            );
            break;

        case 'popular':
            $base_args['meta_key'] = 'total_sales';
            $base_args['orderby'] = 'meta_value_num';
            $base_args['order'] = 'DESC';
            break;
    }

    // Get product IDs
    $product_query = new WP_Query($base_args);
    $product_ids = $product_query->posts;

    if (!empty($product_ids)) {
        // Get categories for these products
        $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => true,
            'object_ids' => $product_ids,
            'number' => 10,
            'exclude' => array(get_option('default_product_cat'))
        ));
    } else {
        // Fallback to all categories
        $categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => true,
            'number' => 10,
            'exclude' => array(get_option('default_product_cat'))
        ));
    }

    // Generate categories HTML
    ob_start();
    echo '<li class="category-item active" data-category="all">All</li>';

    if (!empty($categories) && !is_wp_error($categories)) {
        foreach ($categories as $category) {
            echo '<li class="category-item" data-category="' . esc_attr($category->slug) . '">' . esc_html($category->name) . '</li>';
        }
    }

    $categories_html = ob_get_clean();

    // Send success response
    wp_send_json_success(array(
        'categories' => $categories_html
    ));

    wp_die();
}
add_action('wp_ajax_load_featured_categories', 'tendeal_load_featured_categories');
add_action('wp_ajax_nopriv_load_featured_categories', 'tendeal_load_featured_categories');

/**
 * Override the WooCommerce shop template with our dynamic filters template
 */
function tendeal_override_woocommerce_shop_template($template) {
    // Skip this override if we're on a WCFM vendor store page
    if (function_exists('wcfmmp_is_store_page') && wcfmmp_is_store_page()) {
        return $template;
    }

    // Apply to all product archive pages: shop, category, tag, brand, etc.
    if (is_shop() || is_product_category() || is_product_tag() || is_tax('product_brand') ||
        (function_exists('is_product_taxonomy') && is_product_taxonomy())) {

        // First try to use our custom archive template
        $new_template = locate_template('woocommerce/archive-product-dynamic.php');
        if (!empty($new_template)) {
            return $new_template;
        }

        // If that's not found, use the dynamic filters template directly
        $dynamic_template = locate_template('shop-dynamic-filters.php');
        if (!empty($dynamic_template)) {
            return $dynamic_template;
        }
    }
    return $template;
}
add_filter('template_include', 'tendeal_override_woocommerce_shop_template', 99);

/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Use the exact header
 */
function tendeal_use_exact_header($template) {
    if ($template === get_template_directory() . '/header.php') {
        return get_template_directory() . '/header-exact.php';
    }
    return $template;
}
add_filter('template_include', 'tendeal_use_exact_header');

/**
 * Use custom template for WCFM store pages
 */
function tendeal_use_custom_wcfm_template($template) {
    // Check if we're on a WCFM store page
    if (function_exists('wcfmmp_is_store_page') && wcfmmp_is_store_page()) {
        // First, check if WCFM has already set a template
        if (strpos($template, 'wcfm') !== false || strpos($template, 'wc-frontend-manager') !== false) {
            // WCFM has already set its template, let's not override it
            return $template;
        }

        // If WCFM hasn't set a template, use our custom one
        $custom_template = locate_template('wcfm-store-template.php');
        if (!empty($custom_template)) {
            return $custom_template;
        }
    }
    return $template;
}
add_filter('template_include', 'tendeal_use_custom_wcfm_template', 999); // High priority to run after WCFM's own template handling

/**
 * Remove shop content from WCFM store pages
 */
function tendeal_remove_shop_content_from_wcfm_store() {
    if (function_exists('wcfmmp_is_store_page') && wcfmmp_is_store_page()) {
        // Remove any actions that might be adding shop content after the footer
        remove_all_actions('woocommerce_after_main_content');
        remove_all_actions('woocommerce_sidebar');
        remove_all_actions('woocommerce_after_shop_loop');
        remove_all_actions('woocommerce_no_products_found');
    }
}

/**
 * Fix double pagination issue on shop pages
 */
function tendeal_fix_double_pagination() {
    // Check if we're on a shop-related page
    if (function_exists('is_shop') && function_exists('is_product_category') &&
        function_exists('is_product_tag') && function_exists('is_tax')) {

        if (is_shop() || is_product_category() || is_product_tag() || is_tax('product_brand')) {
            // Remove WooCommerce default pagination to prevent duplicates
            remove_action('woocommerce_after_shop_loop', 'woocommerce_pagination', 10);
        }
    }
}
add_action('wp', 'tendeal_fix_double_pagination', 5);

/**
 * Add simple pagination fix script to shop pages
 */
function tendeal_add_simple_pagination_fix() {
    // Check if we're on a shop-related page
    if (function_exists('is_shop') && function_exists('is_product_category') &&
        function_exists('is_product_tag') && function_exists('is_tax')) {

        if (is_shop() || is_product_category() || is_product_tag() || is_tax('product_brand')) {
            ?>
<script>
jQuery(document).ready(function($) {
  // Simple pagination duplicate removal
  function removeDuplicates() {
    var paginations = $('.woocommerce-pagination, .shop-pagination');
    if (paginations.length > 1) {
      paginations.slice(1).remove();
    }
  }

  // Fix pagination links
  function fixPaginationLinks() {
    $('.woocommerce-pagination a, .shop-pagination a').each(function() {
      // Remove any existing click handlers that might prevent navigation
      $(this).off('click.ajax');

      // Ensure the link is clickable
      $(this).css({
        'pointer-events': 'auto',
        'cursor': 'pointer'
      });
    });
  }

  // Run on page load
  removeDuplicates();
  fixPaginationLinks();

  // Run after AJAX calls
  $(document).ajaxComplete(function() {
    setTimeout(function() {
      removeDuplicates();
      fixPaginationLinks();
    }, 100);
  });
});
</script>
<?php
        }
    }
}
add_action('wp_footer', 'tendeal_add_simple_pagination_fix');
add_action('wp', 'tendeal_remove_shop_content_from_wcfm_store', 999);

/**
 * Fix WooCommerce product count and pagination by ensuring proper product visibility
 */
function tendeal_fix_woocommerce_product_visibility($query_args) {
    // Ensure we have proper tax_query array
    if (!isset($query_args['tax_query'])) {
        $query_args['tax_query'] = array();
    }

    // Add relation if not set and we have multiple tax queries
    if (count($query_args['tax_query']) > 1 && !isset($query_args['tax_query']['relation'])) {
        $query_args['tax_query']['relation'] = 'AND';
    }

    // Check if we already have a product_visibility filter
    $has_visibility_filter = false;
    foreach ($query_args['tax_query'] as $tax_query) {
        if (is_array($tax_query) && isset($tax_query['taxonomy']) && $tax_query['taxonomy'] === 'product_visibility') {
            $has_visibility_filter = true;
            break;
        }
    }

    // Add product visibility filter using taxonomy (modern WooCommerce)
    if (!$has_visibility_filter) {
        $query_args['tax_query'][] = array(
            'taxonomy' => 'product_visibility',
            'field' => 'name',
            'terms' => array('exclude-from-catalog', 'exclude-from-search'),
            'operator' => 'NOT IN'
        );
    }

    // Ensure we have proper meta_query array for stock status
    if (!isset($query_args['meta_query'])) {
        $query_args['meta_query'] = array();
    }

    // Add relation if not set and we have multiple meta queries
    if (count($query_args['meta_query']) > 1 && !isset($query_args['meta_query']['relation'])) {
        $query_args['meta_query']['relation'] = 'AND';
    }

    // Add stock status filter if not already present
    $has_stock_filter = false;
    foreach ($query_args['meta_query'] as $meta_query) {
        if (is_array($meta_query) && isset($meta_query['key']) && $meta_query['key'] === '_stock_status') {
            $has_stock_filter = true;
            break;
        }
    }

    if (!$has_stock_filter) {
        $query_args['meta_query'][] = array(
            'key' => '_stock_status',
            'value' => array('instock', 'onbackorder'),
            'compare' => 'IN'
        );
    }

    return $query_args;
}

/**
 * Get proper WooCommerce product query using WC functions
 */
function tendeal_get_wc_product_query($args = array()) {
    // Use WooCommerce's built-in product query
    $default_args = array(
        'status' => 'publish',
        'visibility' => 'catalog',
        'stock_status' => array('instock', 'onbackorder'),
        'limit' => 12,
        'paginate' => true
    );

    $args = wp_parse_args($args, $default_args);

    // Use WooCommerce's product query
    return wc_get_products($args);
}

/**
 * Debug function to check product count differences
 */
function tendeal_debug_product_count() {
    // Only run for admin users and on shop pages
    if (!current_user_can('manage_options') || !is_shop()) {
        return;
    }

    // Get total published products
    $total_products = wp_count_posts('product');
    $published_count = $total_products->publish;

    // Test different visibility methods

    // Method 1: Using _visibility meta key (older WooCommerce)
    $visible_meta_query = new WP_Query(array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => '_visibility',
                'value' => array('catalog', 'visible'),
                'compare' => 'IN'
            )
        )
    ));

    // Method 2: Using product_visibility taxonomy (newer WooCommerce)
    $visible_tax_query = new WP_Query(array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'post_status' => 'publish',
        'tax_query' => array(
            array(
                'taxonomy' => 'product_visibility',
                'field' => 'name',
                'terms' => array('exclude-from-catalog', 'exclude-from-search'),
                'operator' => 'NOT IN'
            )
        )
    ));

    // Method 3: Using WooCommerce's built-in function
    $wc_visible_query = new WP_Query(array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'post_status' => 'publish'
    ));

    // Method 4: Just published products with stock status
    $stock_query = new WP_Query(array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'fields' => 'ids',
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => '_stock_status',
                'value' => array('instock', 'onbackorder'),
                'compare' => 'IN'
            )
        )
    ));

    // Method 5: Using WooCommerce's built-in function
    $wc_products = wc_get_products(array(
        'status' => 'publish',
        'visibility' => 'catalog',
        'stock_status' => array('instock', 'onbackorder'),
        'limit' => -1,
        'return' => 'ids'
    ));

    $visible_meta_count = $visible_meta_query->found_posts;
    $visible_tax_count = $visible_tax_query->found_posts;
    $wc_visible_count = $wc_visible_query->found_posts;
    $stock_count = $stock_query->found_posts;
    $wc_function_count = count($wc_products);

    // Add debug info to footer for admin users
    add_action('wp_footer', function() use ($published_count, $visible_meta_count, $visible_tax_count, $wc_visible_count, $stock_count, $wc_function_count) {
        // if (current_user_can('manage_options')) {
        //     echo '<div style="position: fixed; bottom: 10px; right: 10px; background: #000; color: #fff; padding: 10px; z-index: 9999; font-size: 11px; max-width: 300px;">';
        //     echo 'Debug Product Counts:<br>';
        //     echo 'Total Published: ' . $published_count . '<br>';
        //     echo 'Meta Visibility: ' . $visible_meta_count . '<br>';
        //     echo 'Tax Visibility: ' . $visible_tax_count . '<br>';
        //     echo 'WC Default: ' . $wc_visible_count . '<br>';
        //     echo 'Stock Filter: ' . $stock_count . '<br>';
        //     echo '<strong>WC Function: ' . $wc_function_count . '</strong><br>';
        //     echo 'Expected: 46 pages = ~552 products (46*12)';
        //     echo '</div>';
        // }
    });
}
add_action('wp', 'tendeal_debug_product_count');

/**
 * Set WooCommerce products per page to 12
 */
function tendeal_set_products_per_page() {
    return 12;
}
add_filter('loop_shop_per_page', 'tendeal_set_products_per_page', 20);

/**
 * Optimized Product Query System for Large Catalogs
 * Replaces inefficient WP_Query with optimized WooCommerce functions
 */
class Tendeal_Optimized_Product_Query {

    private static $instance = null;
    private $cache_group = 'tendeal_product_queries';
    private $cache_expiry = 3600; // 1 hour

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        // Initialize cache group
        wp_cache_add_global_groups(array($this->cache_group));
    }

    /**
     * Get optimized product query using WooCommerce functions
     * This is much faster than WP_Query for large product catalogs
     */
    public function get_products($args = array()) {
        // Generate cache key based on arguments
        $cache_key = 'products_' . md5(serialize($args));

        // Try to get cached results first
        $cached_result = wp_cache_get($cache_key, $this->cache_group);
        if (false !== $cached_result && !isset($_GET['refresh_cache'])) {
            return $cached_result;
        }

        // Default arguments optimized for performance
        $default_args = array(
            'status' => 'publish',
            'visibility' => 'catalog',
            'stock_status' => array('instock', 'onbackorder'),
            'limit' => 12,
            'paginate' => true,
            'return' => 'objects' // Return full objects for display
        );

        $args = wp_parse_args($args, $default_args);

        // Use WooCommerce's optimized product query
        $result = wc_get_products($args);

        // Cache the result
        wp_cache_set($cache_key, $result, $this->cache_group, $this->cache_expiry);

        return $result;
    }

    /**
     * Get product count without loading full objects (much faster)
     */
    public function get_product_count($args = array()) {
        $cache_key = 'count_' . md5(serialize($args));

        $cached_count = wp_cache_get($cache_key, $this->cache_group);
        if (false !== $cached_count && !isset($_GET['refresh_cache'])) {
            return $cached_count;
        }

        // Use optimized count query
        $count_args = array_merge($args, array(
            'return' => 'ids',
            'limit' => -1,
            'paginate' => false
        ));

        $products = wc_get_products($count_args);
        $count = is_array($products) ? count($products) : 0;

        // Cache for longer since counts change less frequently
        wp_cache_set($cache_key, $count, $this->cache_group, $this->cache_expiry * 2);

        return $count;
    }

    /**
     * Get product IDs only (fastest query for filtering)
     */
    public function get_product_ids($args = array()) {
        $cache_key = 'ids_' . md5(serialize($args));

        $cached_ids = wp_cache_get($cache_key, $this->cache_group);
        if (false !== $cached_ids && !isset($_GET['refresh_cache'])) {
            return $cached_ids;
        }

        $id_args = array_merge($args, array(
            'return' => 'ids',
            'limit' => -1,
            'paginate' => false
        ));

        $product_ids = wc_get_products($id_args);

        // Cache for longer
        wp_cache_set($cache_key, $product_ids, $this->cache_group, $this->cache_expiry * 2);

        return $product_ids;
    }

    /**
     * Clear cache when products are updated
     */
    public function clear_cache($product_id = null) {
        // Clear all cached queries
        wp_cache_flush_group($this->cache_group);

        // Also clear WooCommerce's own cache
        if (function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients($product_id);
        }
    }
}

// Initialize the optimized query system
function tendeal_init_optimized_queries() {
    return Tendeal_Optimized_Product_Query::get_instance();
}

// Hook into product update events to clear cache
add_action('save_post_product', function($product_id) {
    Tendeal_Optimized_Product_Query::get_instance()->clear_cache($product_id);
});
add_action('woocommerce_update_product', function($product_id) {
    Tendeal_Optimized_Product_Query::get_instance()->clear_cache($product_id);
});
add_action('woocommerce_new_product', function($product_id) {
    Tendeal_Optimized_Product_Query::get_instance()->clear_cache($product_id);
});

/**
 * Optimized Filter Data System for Large Product Catalogs
 * Uses direct database queries and caching to avoid processing all products
 */
class Tendeal_Optimized_Filter_Data {

    private static $instance = null;
    private $cache_group = 'tendeal_filter_data';
    private $cache_expiry = 7200; // 2 hours

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        wp_cache_add_global_groups(array($this->cache_group));
    }

    /**
     * Get filter data using optimized database queries
     */
    public function get_filter_data($context = 'shop', $context_id = null) {
        $cache_key = "filter_data_{$context}";
        if ($context_id) {
            $cache_key .= "_{$context_id}";
        }

        // Try cache first
        $cached_data = wp_cache_get($cache_key, $this->cache_group);
        if (false !== $cached_data && !isset($_GET['refresh_cache'])) {
            return $cached_data;
        }

        $start_time = microtime(true);

        // Get base product IDs using optimized query
        $base_product_ids = $this->get_base_product_ids($context, $context_id);

        if (empty($base_product_ids)) {
            return $this->get_empty_filter_data();
        }

        // Use direct database queries for better performance
        global $wpdb;

        // Convert array to comma-separated string for SQL IN clause
        $product_ids_str = implode(',', array_map('intval', $base_product_ids));

        // Get categories with counts
        $categories = $this->get_categories_with_counts($product_ids_str);

        // Get brands with counts
        $brands = $this->get_brands_with_counts($product_ids_str);

        // Get price range
        $price_range = $this->get_price_range($product_ids_str);

        // Get rating counts
        $rating_counts = $this->get_rating_counts($product_ids_str);

        // Get attributes with counts
        $attributes = $this->get_attributes_with_counts($product_ids_str);

        $processing_time = microtime(true) - $start_time;

        $filter_data = array(
            'categories' => $categories,
            'brands' => $brands,
            'attributes' => $attributes,
            'price_range' => $price_range,
            'rating_counts' => $rating_counts,
            'total_products' => count($base_product_ids),
            'processing_time' => $processing_time,
            'generated_at' => current_time('mysql')
        );

        // Cache the result
        wp_cache_set($cache_key, $filter_data, $this->cache_group, $this->cache_expiry);

        return $filter_data;
    }

    /**
     * Get base product IDs using optimized WooCommerce query
     */
    private function get_base_product_ids($context, $context_id) {
        $args = array(
            'status' => 'publish',
            'visibility' => 'catalog',
            'stock_status' => array('instock', 'onbackorder'),
            'return' => 'ids',
            'limit' => -1
        );

        // Add context-specific filters
        if ($context === 'category' && $context_id) {
            $args['category'] = array($context_id);
        } elseif ($context === 'brand' && $context_id) {
            $args['tag'] = array($context_id); // Assuming brands are stored as tags
        }

        return wc_get_products($args);
    }

    /**
     * Get categories with product counts using direct database query
     */
    private function get_categories_with_counts($product_ids_str) {
        global $wpdb;

        $sql = "
            SELECT t.term_id, t.name, t.slug, COUNT(DISTINCT p.ID) as product_count
            FROM {$wpdb->terms} t
            INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
            INNER JOIN {$wpdb->term_relationships} tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
            INNER JOIN {$wpdb->posts} p ON tr.object_id = p.ID
            WHERE tt.taxonomy = 'product_cat'
            AND p.ID IN ({$product_ids_str})
            AND p.post_status = 'publish'
            GROUP BY t.term_id
            HAVING product_count > 0
            ORDER BY t.name ASC
        ";

        return $wpdb->get_results($sql);
    }

    /**
     * Get brands with product counts using direct database query
     */
    private function get_brands_with_counts($product_ids_str) {
        global $wpdb;

        $sql = "
            SELECT t.term_id, t.name, t.slug, COUNT(DISTINCT p.ID) as product_count
            FROM {$wpdb->terms} t
            INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
            INNER JOIN {$wpdb->term_relationships} tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
            INNER JOIN {$wpdb->posts} p ON tr.object_id = p.ID
            WHERE tt.taxonomy = 'product_brand'
            AND p.ID IN ({$product_ids_str})
            AND p.post_status = 'publish'
            GROUP BY t.term_id
            HAVING product_count > 0
            ORDER BY t.name ASC
        ";

        return $wpdb->get_results($sql);
    }

    /**
     * Get price range using direct database query
     */
    private function get_price_range($product_ids_str) {
        global $wpdb;

        $sql = "
            SELECT
                MIN(CAST(pm.meta_value AS DECIMAL(10,2))) as min_price,
                MAX(CAST(pm.meta_value AS DECIMAL(10,2))) as max_price
            FROM {$wpdb->postmeta} pm
            WHERE pm.meta_key = '_price'
            AND pm.post_id IN ({$product_ids_str})
            AND pm.meta_value != ''
            AND pm.meta_value IS NOT NULL
        ";

        $result = $wpdb->get_row($sql);

        return array(
            'min' => $result ? floatval($result->min_price) : 0,
            'max' => $result ? floatval($result->max_price) : 1000
        );
    }

    /**
     * Get rating counts using direct database query
     */
    private function get_rating_counts($product_ids_str) {
        global $wpdb;

        $sql = "
            SELECT
                FLOOR(CAST(pm.meta_value AS DECIMAL(3,2))) as rating,
                COUNT(*) as count
            FROM {$wpdb->postmeta} pm
            WHERE pm.meta_key = '_wc_average_rating'
            AND pm.post_id IN ({$product_ids_str})
            AND pm.meta_value != ''
            AND pm.meta_value > 0
            GROUP BY FLOOR(CAST(pm.meta_value AS DECIMAL(3,2)))
            ORDER BY rating DESC
        ";

        $results = $wpdb->get_results($sql);
        $rating_counts = array(5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0);

        foreach ($results as $result) {
            $rating = intval($result->rating);
            if ($rating >= 1 && $rating <= 5) {
                $rating_counts[$rating] = intval($result->count);
            }
        }

        return $rating_counts;
    }

    /**
     * Get attributes with counts using direct database query
     */
    private function get_attributes_with_counts($product_ids_str) {
        global $wpdb;

        // Get all product attribute taxonomies
        $attribute_taxonomies = wc_get_attribute_taxonomies();
        $attributes = array();

        foreach ($attribute_taxonomies as $attribute) {
            $taxonomy = wc_attribute_taxonomy_name($attribute->attribute_name);

            $sql = "
                SELECT t.term_id, t.name, t.slug, COUNT(DISTINCT p.ID) as product_count
                FROM {$wpdb->terms} t
                INNER JOIN {$wpdb->term_taxonomy} tt ON t.term_id = tt.term_id
                INNER JOIN {$wpdb->term_relationships} tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
                INNER JOIN {$wpdb->posts} p ON tr.object_id = p.ID
                WHERE tt.taxonomy = %s
                AND p.ID IN ({$product_ids_str})
                AND p.post_status = 'publish'
                GROUP BY t.term_id
                HAVING product_count > 0
                ORDER BY t.name ASC
            ";

            $terms = $wpdb->get_results($wpdb->prepare($sql, $taxonomy));

            if (!empty($terms)) {
                $attributes[$attribute->attribute_name] = array(
                    'label' => $attribute->attribute_label,
                    'terms' => $terms
                );
            }
        }

        return $attributes;
    }

    /**
     * Return empty filter data structure
     */
    private function get_empty_filter_data() {
        return array(
            'categories' => array(),
            'brands' => array(),
            'attributes' => array(),
            'price_range' => array('min' => 0, 'max' => 1000),
            'rating_counts' => array(5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0),
            'total_products' => 0,
            'processing_time' => 0,
            'generated_at' => current_time('mysql')
        );
    }

    /**
     * Clear filter data cache
     */
    public function clear_cache() {
        wp_cache_flush_group($this->cache_group);
    }
}

// Initialize optimized filter data system
function tendeal_init_optimized_filter_data() {
    return Tendeal_Optimized_Filter_Data::get_instance();
}

/**
 * Database Optimization for Large Product Catalogs
 * Adds necessary indexes for better query performance
 */
class Tendeal_Database_Optimizer {

    private static $instance = null;
    private $indexes_created = false;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('init', array($this, 'maybe_create_indexes'));
        add_action('admin_init', array($this, 'maybe_create_indexes'));
    }

    /**
     * Create database indexes if they don't exist
     */
    public function maybe_create_indexes() {
        // Only run once per request and for admin users
        if ($this->indexes_created || !current_user_can('manage_options')) {
            return;
        }

        $this->indexes_created = true;

        // Check if indexes need to be created
        $indexes_option = get_option('tendeal_db_indexes_created', false);
        if ($indexes_option) {
            return;
        }

        global $wpdb;

        // Suppress errors temporarily
        $wpdb->suppress_errors(true);

        try {
            // Index for product stock status (most common filter)
            $this->create_index_if_not_exists(
                $wpdb->postmeta,
                'idx_stock_status',
                'meta_key, meta_value, post_id',
                "meta_key = '_stock_status'"
            );

            // Index for product prices (for price filtering and sorting)
            $this->create_index_if_not_exists(
                $wpdb->postmeta,
                'idx_price',
                'meta_key, CAST(meta_value AS DECIMAL(10,2)), post_id',
                "meta_key = '_price'"
            );

            // Index for product ratings
            $this->create_index_if_not_exists(
                $wpdb->postmeta,
                'idx_rating',
                'meta_key, CAST(meta_value AS DECIMAL(3,2)), post_id',
                "meta_key = '_wc_average_rating'"
            );

            // Index for product visibility
            $this->create_index_if_not_exists(
                $wpdb->postmeta,
                'idx_visibility',
                'meta_key, meta_value, post_id',
                "meta_key = '_visibility'"
            );

            // Index for product sales (popularity sorting)
            $this->create_index_if_not_exists(
                $wpdb->postmeta,
                'idx_total_sales',
                'meta_key, CAST(meta_value AS UNSIGNED), post_id',
                "meta_key = 'total_sales'"
            );

            // Composite index for posts table (product queries)
            $this->create_index_if_not_exists(
                $wpdb->posts,
                'idx_product_status_type',
                'post_type, post_status, menu_order, post_date',
                "post_type = 'product'"
            );

            // Index for term relationships (category/brand filtering)
            $this->create_index_if_not_exists(
                $wpdb->term_relationships,
                'idx_object_taxonomy',
                'object_id, term_taxonomy_id'
            );

            // Mark indexes as created
            update_option('tendeal_db_indexes_created', true);

            // Log success for debugging
            if (WP_DEBUG) {
                error_log('Tendeal: Database indexes created successfully');
            }

        } catch (Exception $e) {
            // Log error but don't break the site
            if (WP_DEBUG) {
                error_log('Tendeal: Error creating database indexes: ' . $e->getMessage());
            }
        }

        $wpdb->suppress_errors(false);
    }

    /**
     * Create index if it doesn't exist
     */
    private function create_index_if_not_exists($table, $index_name, $columns, $where_clause = '') {
        global $wpdb;

        // Check if index exists
        $index_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
             WHERE table_schema = %s
             AND table_name = %s
             AND index_name = %s",
            DB_NAME,
            $table,
            $index_name
        ));

        if (!$index_exists) {
            $sql = "CREATE INDEX {$index_name} ON {$table} ({$columns})";
            if ($where_clause) {
                $sql .= " WHERE {$where_clause}";
            }

            $result = $wpdb->query($sql);

            if ($result === false) {
                if (WP_DEBUG) {
                    error_log("Failed to create index {$index_name}: " . $wpdb->last_error);
                }
            }
        }
    }

    /**
     * Remove indexes (for cleanup)
     */
    public function remove_indexes() {
        global $wpdb;

        $indexes = array(
            $wpdb->postmeta => array('idx_stock_status', 'idx_price', 'idx_rating', 'idx_visibility', 'idx_total_sales'),
            $wpdb->posts => array('idx_product_status_type'),
            $wpdb->term_relationships => array('idx_object_taxonomy')
        );

        foreach ($indexes as $table => $table_indexes) {
            foreach ($table_indexes as $index_name) {
                $wpdb->query("DROP INDEX IF EXISTS {$index_name} ON {$table}");
            }
        }

        delete_option('tendeal_db_indexes_created');
    }

    /**
     * Get database performance statistics
     */
    public function get_performance_stats() {
        global $wpdb;

        $stats = array();

        // Get table sizes
        $table_stats = $wpdb->get_results("
            SELECT
                table_name,
                table_rows,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
            FROM information_schema.tables
            WHERE table_schema = '" . DB_NAME . "'
            AND table_name IN ('{$wpdb->posts}', '{$wpdb->postmeta}', '{$wpdb->terms}', '{$wpdb->term_relationships}')
        ");

        foreach ($table_stats as $table) {
            $stats['tables'][$table->table_name] = array(
                'rows' => $table->table_rows,
                'size_mb' => $table->size_mb
            );
        }

        // Get index information
        $index_stats = $wpdb->get_results("
            SELECT
                table_name,
                index_name,
                cardinality
            FROM information_schema.statistics
            WHERE table_schema = '" . DB_NAME . "'
            AND index_name LIKE 'idx_%'
        ");

        foreach ($index_stats as $index) {
            $stats['indexes'][$index->table_name][$index->index_name] = $index->cardinality;
        }

        return $stats;
    }
}

// Initialize database optimizer
function tendeal_init_database_optimizer() {
    return Tendeal_Database_Optimizer::get_instance();
}

// Hook into theme activation to create indexes
add_action('after_switch_theme', function() {
    // Delay index creation to avoid conflicts
    wp_schedule_single_event(time() + 30, 'tendeal_create_db_indexes');
});

add_action('tendeal_create_db_indexes', function() {
    Tendeal_Database_Optimizer::get_instance()->maybe_create_indexes();
});

/**
 * Performance Monitoring and Admin Dashboard
 */
class Tendeal_Performance_Monitor {

    private static $instance = null;
    private $performance_data = array();

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_clear_shop_cache', array($this, 'clear_shop_cache'));
        add_action('wp_footer', array($this, 'track_page_performance'));
    }

    /**
     * Add admin menu for performance monitoring
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Shop Performance', 'tendeal'),
            __('Shop Performance', 'tendeal'),
            'manage_options',
            'shop-performance',
            array($this, 'admin_page')
        );
    }

    /**
     * Admin page for performance monitoring
     */
    public function admin_page() {
        // Get performance statistics
        $db_optimizer = Tendeal_Database_Optimizer::get_instance();
        $db_stats = $db_optimizer->get_performance_stats();

        // Get cache statistics
        $cache_stats = $this->get_cache_statistics();

        // Get recent performance data
        $recent_performance = get_option('tendeal_recent_performance', array());

        ?>
        <div class="wrap">
            <h1><?php esc_html_e('Shop Performance Dashboard', 'tendeal'); ?></h1>

            <div class="notice notice-info">
                <p><strong><?php esc_html_e('Shop Optimization Status:', 'tendeal'); ?></strong>
                   <?php esc_html_e('Optimized for large product catalogs (20000+ products)', 'tendeal'); ?></p>
            </div>

            <!-- Performance Overview -->
            <div class="postbox">
                <h2 class="hndle"><?php esc_html_e('Performance Overview', 'tendeal'); ?></h2>
                <div class="inside">
                    <table class="widefat">
                        <tr>
                            <td><strong><?php esc_html_e('Database Indexes:', 'tendeal'); ?></strong></td>
                            <td><?php echo get_option('tendeal_db_indexes_created') ?
                                '<span style="color: green;">✓ Created</span>' :
                                '<span style="color: red;">✗ Not Created</span>'; ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php esc_html_e('Object Caching:', 'tendeal'); ?></strong></td>
                            <td><?php echo wp_using_ext_object_cache() ?
                                '<span style="color: green;">✓ Active</span>' :
                                '<span style="color: orange;">⚠ Not Active (Recommended: Redis/Memcached)</span>'; ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php esc_html_e('Total Products:', 'tendeal'); ?></strong></td>
                            <td><?php echo number_format(wp_count_posts('product')->publish); ?></td>
                        </tr>
                        <tr>
                            <td><strong><?php esc_html_e('Filter Cache Status:', 'tendeal'); ?></strong></td>
                            <td><?php echo !empty($cache_stats['filter_cache']) ?
                                '<span style="color: green;">✓ Active (' . count($cache_stats['filter_cache']) . ' entries)</span>' :
                                '<span style="color: orange;">⚠ Empty</span>'; ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Database Statistics -->
            <?php if (!empty($db_stats)) : ?>
            <div class="postbox">
                <h2 class="hndle"><?php esc_html_e('Database Statistics', 'tendeal'); ?></h2>
                <div class="inside">
                    <h3><?php esc_html_e('Table Sizes', 'tendeal'); ?></h3>
                    <table class="widefat">
                        <thead>
                            <tr>
                                <th><?php esc_html_e('Table', 'tendeal'); ?></th>
                                <th><?php esc_html_e('Rows', 'tendeal'); ?></th>
                                <th><?php esc_html_e('Size (MB)', 'tendeal'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($db_stats['tables'] as $table => $stats) : ?>
                            <tr>
                                <td><?php echo esc_html($table); ?></td>
                                <td><?php echo number_format($stats['rows']); ?></td>
                                <td><?php echo esc_html($stats['size_mb']); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <?php if (!empty($db_stats['indexes'])) : ?>
                    <h3><?php esc_html_e('Optimized Indexes', 'tendeal'); ?></h3>
                    <table class="widefat">
                        <thead>
                            <tr>
                                <th><?php esc_html_e('Table', 'tendeal'); ?></th>
                                <th><?php esc_html_e('Index', 'tendeal'); ?></th>
                                <th><?php esc_html_e('Cardinality', 'tendeal'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($db_stats['indexes'] as $table => $indexes) : ?>
                                <?php foreach ($indexes as $index => $cardinality) : ?>
                                <tr>
                                    <td><?php echo esc_html($table); ?></td>
                                    <td><?php echo esc_html($index); ?></td>
                                    <td><?php echo number_format($cardinality); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Cache Management -->
            <div class="postbox">
                <h2 class="hndle"><?php esc_html_e('Cache Management', 'tendeal'); ?></h2>
                <div class="inside">
                    <p><?php esc_html_e('Clear cached data to force regeneration with latest product information.', 'tendeal'); ?></p>
                    <p>
                        <button type="button" class="button button-secondary" id="clear-filter-cache">
                            <?php esc_html_e('Clear Filter Cache', 'tendeal'); ?>
                        </button>
                        <button type="button" class="button button-secondary" id="clear-product-cache">
                            <?php esc_html_e('Clear Product Cache', 'tendeal'); ?>
                        </button>
                        <button type="button" class="button button-primary" id="clear-all-cache">
                            <?php esc_html_e('Clear All Shop Cache', 'tendeal'); ?>
                        </button>
                    </p>
                    <div id="cache-status"></div>
                </div>
            </div>

            <!-- Performance Recommendations -->
            <div class="postbox">
                <h2 class="hndle"><?php esc_html_e('Performance Recommendations', 'tendeal'); ?></h2>
                <div class="inside">
                    <ul>
                        <?php if (!wp_using_ext_object_cache()) : ?>
                        <li><strong><?php esc_html_e('Install Object Caching:', 'tendeal'); ?></strong>
                            <?php esc_html_e('Install Redis or Memcached for significant performance improvements.', 'tendeal'); ?></li>
                        <?php endif; ?>

                        <?php if (!get_option('tendeal_db_indexes_created')) : ?>
                        <li><strong><?php esc_html_e('Database Indexes:', 'tendeal'); ?></strong>
                            <?php esc_html_e('Database indexes are not created. They will be created automatically.', 'tendeal'); ?></li>
                        <?php endif; ?>

                        <li><strong><?php esc_html_e('Use Optimized Shop Template:', 'tendeal'); ?></strong>
                            <?php esc_html_e('Set your shop page to use the "Optimized Shop for Large Catalogs" template.', 'tendeal'); ?></li>

                        <li><strong><?php esc_html_e('Monitor Performance:', 'tendeal'); ?></strong>
                            <?php esc_html_e('Regularly check this dashboard and clear cache when needed.', 'tendeal'); ?></li>
                    </ul>
                </div>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#clear-filter-cache, #clear-product-cache, #clear-all-cache').on('click', function() {
                var action = 'clear_shop_cache';
                var type = $(this).attr('id').replace('clear-', '').replace('-cache', '');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: action,
                        type: type,
                        nonce: '<?php echo wp_create_nonce('clear_shop_cache'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#cache-status').html('<div class="notice notice-success"><p>' + response.data + '</p></div>');
                        } else {
                            $('#cache-status').html('<div class="notice notice-error"><p>' + response.data + '</p></div>');
                        }
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * AJAX handler for clearing cache
     */
    public function clear_shop_cache() {
        check_ajax_referer('clear_shop_cache', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'tendeal'));
        }

        $type = sanitize_text_field($_POST['type']);

        switch ($type) {
            case 'filter':
                Tendeal_Optimized_Filter_Data::get_instance()->clear_cache();
                wp_send_json_success(__('Filter cache cleared successfully.', 'tendeal'));
                break;

            case 'product':
                Tendeal_Optimized_Product_Query::get_instance()->clear_cache();
                wp_send_json_success(__('Product cache cleared successfully.', 'tendeal'));
                break;

            case 'all':
                Tendeal_Optimized_Filter_Data::get_instance()->clear_cache();
                Tendeal_Optimized_Product_Query::get_instance()->clear_cache();
                wp_cache_flush();
                wp_send_json_success(__('All shop cache cleared successfully.', 'tendeal'));
                break;

            default:
                wp_send_json_error(__('Invalid cache type.', 'tendeal'));
        }
    }

    /**
     * Get cache statistics
     */
    private function get_cache_statistics() {
        $stats = array();

        // This is a simplified version - in a real implementation,
        // you'd query the actual cache backend for statistics
        $stats['filter_cache'] = get_option('tendeal_filter_cache_keys', array());
        $stats['product_cache'] = get_option('tendeal_product_cache_keys', array());

        return $stats;
    }

    /**
     * Track page performance
     */
    public function track_page_performance() {
        if (is_shop() || is_product_category() || is_product_tag()) {
            ?>
            <script>
            if (window.performance && window.performance.timing) {
                var perfData = window.performance.timing;
                var pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;

                // Send performance data to server (simplified)
                if (pageLoadTime > 0) {
                    console.log('Shop page load time: ' + pageLoadTime + 'ms');
                }
            }
            </script>
            <?php
        }
    }
}

// Initialize performance monitor
function tendeal_init_performance_monitor() {
    return Tendeal_Performance_Monitor::get_instance();
}

// Initialize all optimization systems
add_action('init', function() {
    tendeal_init_optimized_queries();
    tendeal_init_optimized_filter_data();
    tendeal_init_database_optimizer();
    tendeal_init_performance_monitor();
});

/**
 * Optimized Cart AJAX Handlers
 * Handles all cart operations with proper error handling and validation
 */
class Tendeal_Cart_Ajax_Handler {

    private static $instance = null;

    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        // AJAX handlers for logged in and non-logged in users
        add_action('wp_ajax_update_cart_item_quantity', array($this, 'update_cart_item_quantity'));
        add_action('wp_ajax_nopriv_update_cart_item_quantity', array($this, 'update_cart_item_quantity'));

        add_action('wp_ajax_remove_cart_item', array($this, 'remove_cart_item'));
        add_action('wp_ajax_nopriv_remove_cart_item', array($this, 'remove_cart_item'));

        add_action('wp_ajax_remove_selected_cart_items', array($this, 'remove_selected_cart_items'));
        add_action('wp_ajax_nopriv_remove_selected_cart_items', array($this, 'remove_selected_cart_items'));

        add_action('wp_ajax_update_cart_address', array($this, 'update_cart_address'));
        add_action('wp_ajax_nopriv_update_cart_address', array($this, 'update_cart_address'));

        add_action('wp_ajax_apply_coupon', array($this, 'apply_coupon'));
        add_action('wp_ajax_nopriv_apply_coupon', array($this, 'apply_coupon'));

        add_action('wp_ajax_remove_coupon', array($this, 'remove_coupon'));
        add_action('wp_ajax_nopriv_remove_coupon', array($this, 'remove_coupon'));

        add_action('wp_ajax_get_vendor_shipping_methods', array($this, 'get_vendor_shipping_methods'));
        add_action('wp_ajax_nopriv_get_vendor_shipping_methods', array($this, 'get_vendor_shipping_methods'));

        add_action('wp_ajax_update_vendor_shipping_method', array($this, 'update_vendor_shipping_method'));
        add_action('wp_ajax_nopriv_update_vendor_shipping_method', array($this, 'update_vendor_shipping_method'));

        add_action('wp_ajax_get_saved_addresses', array($this, 'get_saved_addresses'));
        add_action('wp_ajax_nopriv_get_saved_addresses', array($this, 'get_saved_addresses'));

        add_action('wp_ajax_set_cart_address', array($this, 'set_cart_address'));
        add_action('wp_ajax_nopriv_set_cart_address', array($this, 'set_cart_address'));

        add_action('wp_ajax_refresh_cart_totals', array($this, 'refresh_cart_totals'));
        add_action('wp_ajax_nopriv_refresh_cart_totals', array($this, 'refresh_cart_totals'));

        add_action('wp_ajax_test_cart_calculation', array($this, 'test_cart_calculation'));
        add_action('wp_ajax_nopriv_test_cart_calculation', array($this, 'test_cart_calculation'));

        add_action('wp_ajax_load_checkout_address', array($this, 'load_checkout_address'));
        add_action('wp_ajax_nopriv_load_checkout_address', array($this, 'load_checkout_address'));
    }

    /**
     * Update cart item quantity
     */
    public function update_cart_item_quantity() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        $cart_item_key = sanitize_text_field($_POST['cart_item_key']);
        $quantity = intval($_POST['quantity']);

        // Validate inputs
        if (empty($cart_item_key) || $quantity < 1) {
            wp_send_json_error(__('Invalid parameters', 'tendeal'));
        }

        // Check if cart item exists
        $cart = WC()->cart->get_cart();
        if (!isset($cart[$cart_item_key])) {
            wp_send_json_error(__('Cart item not found', 'tendeal'));
        }

        // Get product to check stock
        $cart_item = $cart[$cart_item_key];
        $product = $cart_item['data'];

        // Check stock availability
        if (!$product->has_enough_stock($quantity)) {
            wp_send_json_error(sprintf(
                __('Sorry, we do not have enough "%s" in stock to fulfill your request. We only have %d available at this time.', 'tendeal'),
                $product->get_name(),
                $product->get_stock_quantity()
            ));
        }

        // Update quantity
        $updated = WC()->cart->set_quantity($cart_item_key, $quantity, true);

        if ($updated) {
            // Calculate new totals
            WC()->cart->calculate_totals();

            // Get updated cart data
            $response_data = array(
                'item_total' => WC()->cart->get_product_subtotal($product, $quantity),
                'item_price' => WC()->cart->get_product_price($product),
                'cart_totals' => $this->get_cart_totals(),
                'message' => __('Cart updated successfully', 'tendeal')
            );

            wp_send_json_success($response_data);
        } else {
            wp_send_json_error(__('Failed to update cart', 'tendeal'));
        }
    }

    /**
     * Remove single cart item
     */
    public function remove_cart_item() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        $cart_item_key = sanitize_text_field($_POST['cart_item_key']);

        if (empty($cart_item_key)) {
            wp_send_json_error(__('Invalid cart item', 'tendeal'));
        }

        // Remove item from cart
        $removed = WC()->cart->remove_cart_item($cart_item_key);

        if ($removed) {
            // Calculate new totals
            WC()->cart->calculate_totals();

            $response_data = array(
                'cart_totals' => $this->get_cart_totals(),
                'message' => __('Item removed from cart', 'tendeal')
            );

            wp_send_json_success($response_data);
        } else {
            wp_send_json_error(__('Failed to remove item', 'tendeal'));
        }
    }

    /**
     * Remove multiple selected cart items
     */
    public function remove_selected_cart_items() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        $cart_item_keys = $_POST['cart_item_keys'];

        if (empty($cart_item_keys) || !is_array($cart_item_keys)) {
            wp_send_json_error(__('No items selected', 'tendeal'));
        }

        $removed_count = 0;

        foreach ($cart_item_keys as $cart_item_key) {
            $cart_item_key = sanitize_text_field($cart_item_key);
            if (WC()->cart->remove_cart_item($cart_item_key)) {
                $removed_count++;
            }
        }

        if ($removed_count > 0) {
            // Calculate new totals
            WC()->cart->calculate_totals();

            $response_data = array(
                'cart_totals' => $this->get_cart_totals(),
                'message' => sprintf(
                    _n('%d item removed from cart', '%d items removed from cart', $removed_count, 'tendeal'),
                    $removed_count
                )
            );

            wp_send_json_success($response_data);
        } else {
            wp_send_json_error(__('Failed to remove items', 'tendeal'));
        }
    }

    /**
     * Update cart address
     */
    public function update_cart_address() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        // Get address data
        $address_data = array();
        $fields = array('first_name', 'last_name', 'company', 'address_1', 'address_2', 'city', 'state', 'postcode', 'country');

        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                $address_data[$field] = sanitize_text_field($_POST[$field]);
            }
        }

        // Validate required fields
        $required_fields = array('first_name', 'last_name', 'address_1', 'city', 'postcode', 'country');
        foreach ($required_fields as $field) {
            if (empty($address_data[$field])) {
                wp_send_json_error(sprintf(__('%s is required', 'tendeal'), ucfirst(str_replace('_', ' ', $field))));
            }
        }

        // Update customer address
        $customer = WC()->customer;
        foreach ($address_data as $key => $value) {
            $customer->{"set_shipping_$key"}($value);
        }

        // Save customer data
        $customer->save();

        // Recalculate shipping
        WC()->cart->calculate_shipping();
        WC()->cart->calculate_totals();

        wp_send_json_success(array(
            'message' => __('Address updated successfully', 'tendeal'),
            'cart_totals' => $this->get_cart_totals()
        ));
    }

    /**
     * Apply coupon
     */
    public function apply_coupon() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        $coupon_code = sanitize_text_field($_POST['coupon_code']);

        if (empty($coupon_code)) {
            wp_send_json_error(__('Please enter a coupon code', 'tendeal'));
        }

        // Check if coupon is already applied
        $applied_coupons = WC()->cart->get_applied_coupons();
        if (in_array($coupon_code, $applied_coupons)) {
            wp_send_json_error(__('Coupon is already applied', 'tendeal'));
        }

        // Apply coupon
        $applied = WC()->cart->apply_coupon($coupon_code);

        if ($applied) {
            // Calculate new totals
            WC()->cart->calculate_totals();

            // Get coupon details
            $coupon = new WC_Coupon($coupon_code);
            $discount_amount = WC()->cart->get_coupon_discount_amount($coupon_code);

            wp_send_json_success(array(
                'message' => __('Coupon applied successfully', 'tendeal'),
                'cart_totals' => $this->get_enhanced_cart_totals(),
                'coupon' => array(
                    'code' => $coupon_code,
                    'discount' => wc_price($discount_amount),
                    'type' => $coupon->get_discount_type()
                )
            ));
        } else {
            // Get WooCommerce error messages
            $notices = wc_get_notices('error');
            $error_message = !empty($notices) ? $notices[0]['notice'] : __('Invalid coupon code', 'tendeal');
            wc_clear_notices();

            wp_send_json_error($error_message);
        }
    }

    /**
     * Remove coupon
     */
    public function remove_coupon() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        $coupon_code = sanitize_text_field($_POST['coupon_code']);

        if (empty($coupon_code)) {
            wp_send_json_error(__('Invalid coupon code', 'tendeal'));
        }

        // Remove coupon
        $removed = WC()->cart->remove_coupon($coupon_code);

        if ($removed) {
            // Calculate new totals
            WC()->cart->calculate_totals();

            wp_send_json_success(array(
                'message' => __('Coupon removed successfully', 'tendeal'),
                'cart_totals' => $this->get_enhanced_cart_totals()
            ));
        } else {
            wp_send_json_error(__('Failed to remove coupon', 'tendeal'));
        }
    }

    /**
     * Refresh cart totals
     */
    public function refresh_cart_totals() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        // Force cart recalculation
        WC()->cart->calculate_shipping();
        WC()->cart->calculate_totals();

        wp_send_json_success($this->get_enhanced_cart_totals());
    }

    /**
     * Test cart calculation (for debugging)
     */
    public function test_cart_calculation() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        $cart = WC()->cart;

        // Get raw numeric values
        $subtotal_raw = $cart->get_subtotal();
        $shipping_raw = $cart->get_shipping_total();
        $tax_raw = $cart->get_total_tax();
        $discount_raw = $cart->get_discount_total();

        // Manual calculation
        $manual_total = $subtotal_raw + $shipping_raw + $tax_raw - $discount_raw;

        // WooCommerce calculated total
        $wc_total = $cart->get_total();

        wp_send_json_success(array(
            'raw_values' => array(
                'subtotal' => $subtotal_raw,
                'shipping' => $shipping_raw,
                'tax' => $tax_raw,
                'discount' => $discount_raw
            ),
            'calculations' => array(
                'manual_total' => $manual_total,
                'wc_total' => $wc_total,
                'match' => abs($manual_total - $wc_total) < 0.01
            ),
            'formatted_values' => array(
                'subtotal' => wc_price($subtotal_raw),
                'shipping' => wc_price($shipping_raw),
                'tax' => wc_price($tax_raw),
                'discount' => wc_price($discount_raw),
                'manual_total' => wc_price($manual_total),
                'wc_total' => wc_price($wc_total)
            )
        ));
    }

    /**
     * Load checkout address data
     */
    public function load_checkout_address() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'checkout_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        if (!is_user_logged_in()) {
            wp_send_json_error(__('User not logged in', 'tendeal'));
        }

        $address_id = sanitize_text_field($_POST['address_id']);

        if (empty($address_id)) {
            wp_send_json_error(__('Invalid address ID', 'tendeal'));
        }

        // Get address data
        $address_data = tendeal_get_saved_address_data($address_id, get_current_user_id());

        if (!$address_data) {
            wp_send_json_error(__('Address not found', 'tendeal'));
        }

        wp_send_json_success(array(
            'address' => $address_data,
            'message' => __('Address loaded successfully', 'tendeal')
        ));
    }

    /**
     * Get vendor shipping methods
     */
    public function get_vendor_shipping_methods() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        $vendor_id = sanitize_text_field($_POST['vendor_id']);

        if (empty($vendor_id)) {
            wp_send_json_error(__('Invalid vendor ID', 'tendeal'));
        }

        // Get shipping methods for vendor
        $shipping_methods = $this->get_shipping_methods_for_vendor($vendor_id);

        wp_send_json_success(array(
            'methods' => $shipping_methods,
            'vendor_id' => $vendor_id
        ));
    }

    /**
     * Update vendor shipping method
     */
    public function update_vendor_shipping_method() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        $vendor_id = sanitize_text_field($_POST['vendor_id']);
        $shipping_method = sanitize_text_field($_POST['shipping_method']);

        if (empty($vendor_id) || empty($shipping_method)) {
            wp_send_json_error(__('Invalid parameters', 'tendeal'));
        }

        // Update shipping method for vendor
        $updated = $this->update_shipping_method_for_vendor($vendor_id, $shipping_method);

        if ($updated) {
            // Recalculate cart totals
            WC()->cart->calculate_shipping();
            WC()->cart->calculate_totals();

            // Get method details
            $method_details = $this->get_shipping_method_details($shipping_method);

            wp_send_json_success(array(
                'method_name' => $method_details['name'],
                'method_cost' => $method_details['cost'],
                'cart_totals' => $this->get_cart_totals(),
                'message' => __('Shipping method updated successfully', 'tendeal')
            ));
        } else {
            wp_send_json_error(__('Failed to update shipping method', 'tendeal'));
        }
    }

    /**
     * Get shipping methods for a specific vendor
     */
    private function get_shipping_methods_for_vendor($vendor_id) {
        // Get WooCommerce shipping zones and methods
        $shipping_zones = WC_Shipping_Zones::get_zones();
        $methods = array();

        // Add default shipping methods
        $default_methods = array(
            array(
                'id' => 'standard_' . $vendor_id,
                'name' => __('Standard Shipping', 'tendeal'),
                'cost' => wc_price(5.99),
                'delivery_time' => __('5-7 business days', 'tendeal'),
                'description' => __('Standard delivery service', 'tendeal'),
                'estimate' => __('Delivery by ' . date('M j', strtotime('+7 days')), 'tendeal'),
                'icon' => 'truck',
                'tracking' => false,
                'selected' => true
            ),
            array(
                'id' => 'express_' . $vendor_id,
                'name' => __('Express Shipping', 'tendeal'),
                'cost' => wc_price(12.99),
                'delivery_time' => __('2-3 business days', 'tendeal'),
                'description' => __('Fast delivery service', 'tendeal'),
                'estimate' => __('Delivery by ' . date('M j', strtotime('+3 days')), 'tendeal'),
                'icon' => 'zap',
                'tracking' => true,
                'selected' => false
            ),
            array(
                'id' => 'overnight_' . $vendor_id,
                'name' => __('Overnight Shipping', 'tendeal'),
                'cost' => wc_price(24.99),
                'delivery_time' => __('Next business day', 'tendeal'),
                'description' => __('Overnight delivery service', 'tendeal'),
                'estimate' => __('Delivery by ' . date('M j', strtotime('+1 day')), 'tendeal'),
                'icon' => 'clock',
                'tracking' => true,
                'selected' => false
            )
        );

        // Check if vendor has custom shipping methods
        $vendor_methods = get_option("vendor_shipping_methods_{$vendor_id}", array());

        if (!empty($vendor_methods)) {
            $methods = array_merge($methods, $vendor_methods);
        } else {
            $methods = $default_methods;
        }

        // Apply filters to allow customization
        return apply_filters('tendeal_vendor_shipping_methods', $methods, $vendor_id);
    }

    /**
     * Update shipping method for vendor
     */
    private function update_shipping_method_for_vendor($vendor_id, $shipping_method) {
        // Store selected shipping method in session
        $selected_methods = WC()->session->get('vendor_shipping_methods', array());
        $selected_methods[$vendor_id] = $shipping_method;
        WC()->session->set('vendor_shipping_methods', $selected_methods);

        // Also store in user meta if logged in
        if (is_user_logged_in()) {
            $user_methods = get_user_meta(get_current_user_id(), 'vendor_shipping_methods', true);
            if (!is_array($user_methods)) {
                $user_methods = array();
            }
            $user_methods[$vendor_id] = $shipping_method;
            update_user_meta(get_current_user_id(), 'vendor_shipping_methods', $user_methods);
        }

        return true;
    }

    /**
     * Get shipping method details
     */
    private function get_shipping_method_details($method_id) {
        // Extract vendor ID from method ID
        $parts = explode('_', $method_id);
        $method_type = $parts[0];
        $vendor_id = isset($parts[1]) ? $parts[1] : '';

        $details = array(
            'name' => __('Standard Shipping', 'tendeal'),
            'cost' => wc_price(5.99)
        );

        switch ($method_type) {
            case 'standard':
                $details = array(
                    'name' => __('Standard Shipping', 'tendeal'),
                    'cost' => wc_price(5.99)
                );
                break;
            case 'express':
                $details = array(
                    'name' => __('Express Shipping', 'tendeal'),
                    'cost' => wc_price(12.99)
                );
                break;
            case 'overnight':
                $details = array(
                    'name' => __('Overnight Shipping', 'tendeal'),
                    'cost' => wc_price(24.99)
                );
                break;
        }

        return apply_filters('tendeal_shipping_method_details', $details, $method_id);
    }

    /**
     * Get saved addresses for user
     */
    public function get_saved_addresses() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        if (!is_user_logged_in()) {
            wp_send_json_error(__('User not logged in', 'tendeal'));
        }

        $user_id = get_current_user_id();
        $addresses = tendeal_get_user_saved_addresses($user_id);

        wp_send_json_success(array(
            'addresses' => $addresses
        ));
    }

    /**
     * Set cart address from saved address
     */
    public function set_cart_address() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cart_optimized_nonce')) {
            wp_send_json_error(__('Security check failed', 'tendeal'));
        }

        if (!is_user_logged_in()) {
            wp_send_json_error(__('User not logged in', 'tendeal'));
        }

        $address_id = sanitize_text_field($_POST['address_id']);

        if (empty($address_id)) {
            wp_send_json_error(__('Invalid address ID', 'tendeal'));
        }

        // Get address data
        $address_data = tendeal_get_saved_address_data($address_id, get_current_user_id());

        if (!$address_data) {
            wp_send_json_error(__('Address not found', 'tendeal'));
        }

        // Update customer address
        $customer = WC()->customer;
        foreach ($address_data as $key => $value) {
            $method = "set_shipping_{$key}";
            if (method_exists($customer, $method)) {
                $customer->$method($value);
            }
        }

        // Save customer data
        $customer->save();

        // Recalculate shipping and totals
        WC()->cart->calculate_shipping();
        WC()->cart->calculate_totals();

        wp_send_json_success(array(
            'message' => __('Address updated successfully', 'tendeal'),
            'cart_totals' => $this->get_cart_totals()
        ));
    }



    /**
     * Get formatted cart totals
     */
    private function get_cart_totals() {
        $cart = WC()->cart;

        // Get numeric values for proper calculation
        $subtotal_numeric = $cart->get_subtotal();
        $shipping_numeric = $cart->get_shipping_total();
        $tax_numeric = $cart->get_total_tax();
        $discount_numeric = $cart->get_discount_total();

        // Calculate total properly
        $total_numeric = $subtotal_numeric + $shipping_numeric + $tax_numeric - $discount_numeric;

        return array(
            'subtotal' => wc_price($subtotal_numeric),
            'shipping' => wc_price($shipping_numeric),
            'tax' => wc_price($tax_numeric),
            'total' => wc_price($total_numeric),
            'discount' => $discount_numeric > 0 ? wc_price($discount_numeric) : '0',
            'item_count' => $cart->get_cart_contents_count()
        );
    }

    /**
     * Get enhanced cart totals with vendor breakdown and discounts
     */
    private function get_enhanced_cart_totals() {
        $cart = WC()->cart;

        // Get numeric values first for calculations
        $subtotal_numeric = $cart->get_subtotal();
        $shipping_numeric = $cart->get_shipping_total();
        $tax_numeric = $cart->get_total_tax();
        $discount_numeric = $cart->get_discount_total();

        // Calculate total properly
        $total_numeric = $subtotal_numeric + $shipping_numeric + $tax_numeric - $discount_numeric;

        // Format for display
        $totals = array(
            'subtotal' => wc_price($subtotal_numeric),
            'shipping' => wc_price($shipping_numeric),
            'tax' => wc_price($tax_numeric),
            'total' => wc_price($total_numeric),
            'item_count' => $cart->get_cart_contents_count()
        );

        // Get discount total
        $totals['discount'] = $discount_numeric > 0 ? wc_price($discount_numeric) : '0';

        // Get vendor-specific totals
        $vendor_totals = $this->get_vendor_specific_totals();
        if (!empty($vendor_totals)) {
            $totals['vendor_totals'] = $vendor_totals;
        }

        // Get applied coupons
        $applied_coupons = $cart->get_applied_coupons();
        if (!empty($applied_coupons)) {
            $coupons_data = array();
            foreach ($applied_coupons as $coupon_code) {
                $coupon = new WC_Coupon($coupon_code);
                $discount_amount = $cart->get_coupon_discount_amount($coupon_code);

                $coupons_data[] = array(
                    'code' => $coupon_code,
                    'discount' => wc_price($discount_amount),
                    'type' => $coupon->get_discount_type()
                );
            }
            $totals['applied_coupons'] = $coupons_data;
        }

        return $totals;
    }

    /**
     * Get vendor-specific totals
     */
    private function get_vendor_specific_totals() {
        $cart_items = WC()->cart->get_cart();
        $vendor_totals = array();

        foreach ($cart_items as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $product_id = $cart_item['product_id'];

            // Get vendor ID (you may need to adjust this based on your multi-vendor setup)
            $vendor_id = get_post_meta($product_id, '_vendor_id', true);
            if (empty($vendor_id)) {
                $vendor_id = 'default';
            }

            // Initialize vendor totals if not exists
            if (!isset($vendor_totals[$vendor_id])) {
                $vendor_totals[$vendor_id] = array(
                    'subtotal' => 0,
                    'shipping' => 0,
                    'total' => 0,
                    'item_count' => 0
                );
            }

            // Calculate item total
            $item_total = $cart_item['line_total'];
            $vendor_totals[$vendor_id]['subtotal'] += $item_total;
            $vendor_totals[$vendor_id]['total'] += $item_total;
            $vendor_totals[$vendor_id]['item_count'] += $cart_item['quantity'];
        }

        // Format vendor totals
        foreach ($vendor_totals as $vendor_id => &$totals) {
            // Add shipping cost for vendor (calculate before formatting)
            $shipping_cost = $this->get_vendor_shipping_cost($vendor_id);
            $subtotal_numeric = $totals['subtotal'];
            $total_with_shipping = $subtotal_numeric + $shipping_cost;

            // Now format the values
            $totals['subtotal'] = wc_price($subtotal_numeric);
            $totals['shipping'] = wc_price($shipping_cost);
            $totals['total'] = wc_price($total_with_shipping);
        }

        return $vendor_totals;
    }

    /**
     * Get shipping cost for specific vendor
     */
    private function get_vendor_shipping_cost($vendor_id) {
        // Get selected shipping method for vendor
        $selected_methods = WC()->session->get('vendor_shipping_methods', array());
        $selected_method = isset($selected_methods[$vendor_id]) ? $selected_methods[$vendor_id] : 'standard_' . $vendor_id;

        // Get shipping cost based on method
        $parts = explode('_', $selected_method);
        $method_type = $parts[0];

        switch ($method_type) {
            case 'express':
                return 12.99;
            case 'overnight':
                return 24.99;
            case 'standard':
            default:
                return 5.99;
        }
    }
}

// Initialize cart AJAX handler
function tendeal_init_cart_ajax_handler() {
    return Tendeal_Cart_Ajax_Handler::get_instance();
}

add_action('init', 'tendeal_init_cart_ajax_handler');

/**
 * Add vendor shipping costs to WooCommerce cart totals
 */
function tendeal_add_vendor_shipping_to_cart() {
    if ( is_admin() && ! defined( 'DOING_AJAX' ) ) {
        return;
    }

    if ( ! WC()->cart ) {
        return;
    }

    // Get cart items grouped by vendor
    $cart_items_by_vendor = array();
    foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
        $_product = $cart_item['data'];
        $product_id = $_product->get_id();

        $vendor_id = get_post_meta( $product_id, '_wcfm_product_author', true );
        if ( empty( $vendor_id ) ) {
            $vendor_id = 'default';
        }

        $cart_items_by_vendor[$vendor_id][$cart_item_key] = $cart_item;
    }

    // Calculate total vendor shipping
    $total_vendor_shipping = 0;
    foreach ( $cart_items_by_vendor as $vendor_id => $vendor_cart_items ) {
        $shipping_cost = tendeal_get_vendor_shipping_cost( $vendor_id );
        $total_vendor_shipping += $shipping_cost;
    }

    // Add shipping as a fee to the cart
    if ( $total_vendor_shipping > 0 ) {
        WC()->cart->add_fee( __( 'Shipping', 'tendeal' ), $total_vendor_shipping );
    }
}

add_action( 'woocommerce_cart_calculate_fees', 'tendeal_add_vendor_shipping_to_cart' );

/**
 * Helper function to get user saved addresses
 */
function tendeal_get_user_saved_addresses( $user_id ) {
    if ( empty( $user_id ) ) {
        return array();
    }

    // Get addresses from user meta
    $saved_addresses = get_user_meta( $user_id, 'saved_addresses', true );

    if ( !is_array( $saved_addresses ) ) {
        $saved_addresses = array();
    }

    // If no saved addresses, create from current billing/shipping
    if ( empty( $saved_addresses ) ) {
        $customer = new WC_Customer( $user_id );

        // Add billing address if exists
        $billing_address = array(
            'first_name' => $customer->get_billing_first_name(),
            'last_name' => $customer->get_billing_last_name(),
            'company' => $customer->get_billing_company(),
            'address_1' => $customer->get_billing_address_1(),
            'address_2' => $customer->get_billing_address_2(),
            'city' => $customer->get_billing_city(),
            'state' => $customer->get_billing_state(),
            'postcode' => $customer->get_billing_postcode(),
            'country' => $customer->get_billing_country(),
            'phone' => $customer->get_billing_phone()
        );

        if ( !empty( array_filter( $billing_address ) ) ) {
            $saved_addresses[] = array(
                'id' => 'billing',
                'title' => __( 'Billing Address', 'tendeal' ),
                'is_default' => true,
                'formatted_address' => WC()->countries->get_formatted_address( $billing_address ),
                'phone' => $billing_address['phone'],
                'data' => $billing_address
            );
        }

        // Add shipping address if different
        $shipping_address = array(
            'first_name' => $customer->get_shipping_first_name(),
            'last_name' => $customer->get_shipping_last_name(),
            'company' => $customer->get_shipping_company(),
            'address_1' => $customer->get_shipping_address_1(),
            'address_2' => $customer->get_shipping_address_2(),
            'city' => $customer->get_shipping_city(),
            'state' => $customer->get_shipping_state(),
            'postcode' => $customer->get_shipping_postcode(),
            'country' => $customer->get_shipping_country()
        );

        if ( !empty( array_filter( $shipping_address ) ) && $shipping_address !== $billing_address ) {
            $saved_addresses[] = array(
                'id' => 'shipping',
                'title' => __( 'Shipping Address', 'tendeal' ),
                'is_default' => false,
                'formatted_address' => WC()->countries->get_formatted_address( $shipping_address ),
                'phone' => '',
                'data' => $shipping_address
            );
        }
    }

    return $saved_addresses;
}

/**
 * Helper function to get saved address data by ID
 */
function tendeal_get_saved_address_data( $address_id, $user_id ) {
    if ( empty( $address_id ) || empty( $user_id ) ) {
        return false;
    }

    $saved_addresses = tendeal_get_user_saved_addresses( $user_id );

    foreach ( $saved_addresses as $address ) {
        if ( $address['id'] === $address_id ) {
            return $address['data'];
        }
    }

    // Fallback to customer data for billing/shipping
    $customer = new WC_Customer( $user_id );

    if ( $address_id === 'billing' ) {
        return array(
            'first_name' => $customer->get_billing_first_name(),
            'last_name' => $customer->get_billing_last_name(),
            'company' => $customer->get_billing_company(),
            'address_1' => $customer->get_billing_address_1(),
            'address_2' => $customer->get_billing_address_2(),
            'city' => $customer->get_billing_city(),
            'state' => $customer->get_billing_state(),
            'postcode' => $customer->get_billing_postcode(),
            'country' => $customer->get_billing_country(),
            'phone' => $customer->get_billing_phone()
        );
    } elseif ( $address_id === 'shipping' ) {
        return array(
            'first_name' => $customer->get_shipping_first_name(),
            'last_name' => $customer->get_shipping_last_name(),
            'company' => $customer->get_shipping_company(),
            'address_1' => $customer->get_shipping_address_1(),
            'address_2' => $customer->get_shipping_address_2(),
            'city' => $customer->get_shipping_city(),
            'state' => $customer->get_shipping_state(),
            'postcode' => $customer->get_shipping_postcode(),
            'country' => $customer->get_shipping_country()
        );
    }

    return false;
}

/**
 * Debug function for cart totals (remove in production)
 */
function tendeal_debug_cart_totals() {
    if (!WC()->cart || !current_user_can('manage_options')) {
        return;
    }

    $cart = WC()->cart;

    echo "<!-- Cart Totals Debug:\n";
    echo "Subtotal (numeric): " . $cart->get_subtotal() . "\n";
    echo "Shipping (numeric): " . $cart->get_shipping_total() . "\n";
    echo "Tax (numeric): " . $cart->get_total_tax() . "\n";
    echo "Discount (numeric): " . $cart->get_discount_total() . "\n";
    echo "Total (WC calculated): " . $cart->get_total() . "\n";
    echo "Manual calculation: " . ($cart->get_subtotal() + $cart->get_shipping_total() + $cart->get_total_tax() - $cart->get_discount_total()) . "\n";
    echo "-->";
}

// Uncomment the line below to enable debug output
// add_action('wp_footer', 'tendeal_debug_cart_totals');

/**
 * Clear shop filter cache when products are updated
 */
function tendeal_clear_shop_filter_cache($post_id) {
    // Only clear cache for products
    if (get_post_type($post_id) !== 'product') {
        return;
    }

    // Clear all shop filter caches
    global $wpdb;

    // Delete all transients that start with 'tendeal_shop_filters_'
    $wpdb->query(
        $wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_tendeal_shop_filters_%'
        )
    );

    // Also delete timeout transients
    $wpdb->query(
        $wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_timeout_tendeal_shop_filters_%'
        )
    );

    // Clear object cache if available
    if (function_exists('wp_cache_flush_group')) {
        wp_cache_flush_group('tendeal_shop_filters');
    }
}

// Hook into product save/update events
add_action('save_post_product', 'tendeal_clear_shop_filter_cache');
add_action('woocommerce_update_product', 'tendeal_clear_shop_filter_cache');
add_action('woocommerce_new_product', 'tendeal_clear_shop_filter_cache');
add_action('woocommerce_product_set_stock_status', 'tendeal_clear_shop_filter_cache');

// Clear cache when terms are updated (categories, brands, attributes)
function tendeal_clear_shop_filter_cache_on_term_update($term_id, $tt_id, $taxonomy) {
    if (in_array($taxonomy, array('product_cat', 'product_brand')) || strpos($taxonomy, 'pa_') === 0) {
        tendeal_clear_shop_filter_cache(0); // Pass 0 as we're not dealing with a specific product
    }
}
add_action('edited_term', 'tendeal_clear_shop_filter_cache_on_term_update', 10, 3);
add_action('created_term', 'tendeal_clear_shop_filter_cache_on_term_update', 10, 3);
add_action('deleted_term', 'tendeal_clear_shop_filter_cache_on_term_update', 10, 3);

/**
 * Add admin notice for cache refresh option
 */
function tendeal_add_cache_refresh_admin_notice() {
    if (!current_user_can('manage_options')) {
        return;
    }

    // Check if we're on shop-related pages
    $screen = get_current_screen();
    if (!$screen || !in_array($screen->id, array('edit-product', 'product', 'edit-product_cat', 'edit-product_brand'))) {
        return;
    }

    $shop_url = add_query_arg('refresh_cache', '1', wc_get_page_permalink('shop'));

    echo '<div class="notice notice-info is-dismissible">';
    echo '<p><strong>Shop Performance:</strong> Filter data is cached for better performance. ';
    echo '<a href="' . esc_url($shop_url) . '" target="_blank">Refresh shop cache</a> if you notice filter data is outdated.</p>';
    echo '</div>';
}
add_action('admin_notices', 'tendeal_add_cache_refresh_admin_notice');

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
	require get_template_directory() . '/inc/jetpack.php';
}


/**
 * woocommerce theme
 */


require get_template_directory() . '/inc/woocommerce.php';

// Load address card functions
require get_template_directory() . '/inc/address-card-functions.php';



//  add_theme_support('woocommerce');


//  function tendeal_woocommerce_support() {
// 	add_theme_support( 'woocommerce' );
// }

// add_action( 'after_setup_theme', 'tendeal_woocommerce_support' );

//
// function display_product_review_count() {
// 	global $product;

// 	// Get the number of reviews for the product
// 	$review_count = $product->get_review_count();

// 	// Display review count if there are reviews
// 	if ($review_count > 0) {
// 			echo '<p class="product-review-count">' . sprintf( __( '%d reviews', 'woocommerce' ), $review_count ) . '</p>';
// 	} else {
// 			echo '<p class="product-review-count">' . __( 'No reviews yet', 'woocommerce' ) . '</p>';
// 	}
// }

// Hook into the WooCommerce loop to display review count
// add_action( 'woocommerce_after_shop_loop_item_title', 'display_product_review_count', 20 );


// Display review count after star rating
// add_filter('woocommerce_product_get_rating_html', 'custom_review_count_after_rating', 10, 2);
// function custom_review_count_after_rating($rating_html) {
// 	global $product;
//     if ($product->get_review_count() > 0) {
//         $rating_html .= '  ' . $product->get_review_count() . ' reviews';
//     }
//     return $rating_html;
// }

// Remove the text-only approach since WooCommerce sanitizes HTML in button text
remove_filter('woocommerce_product_add_to_cart_text', 'custom_add_to_cart_text_icon');
remove_filter('woocommerce_product_single_add_to_cart_text', 'custom_add_to_cart_text_icon');

// Instead, use a more robust approach to add icons to cart buttons
add_action('wp_footer', 'add_cart_button_icon_script');

function custom_add_to_cart_text_icon() {
    // Return plain text for the button
    return 'Add to Cart';
}

// Add JavaScript to insert Feather icons into cart buttons
function add_cart_button_icon_script() {
    ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Function to add icons to buttons
  function addIconsToButtons() {
    // Add icons to all Add to Cart buttons
    const cartButtons = document.querySelectorAll('.add_to_cart_button, .single_add_to_cart_button');

    cartButtons.forEach(button => {
      // More thorough check for existing icons
      // Skip if already has any icon element or if the first child is not a text node
      if (button.querySelector('i') || button.querySelector('svg') ||
        (button.firstChild && button.firstChild.nodeType !== Node.TEXT_NODE)) {
        return;
      }

      // Create icon element
      const icon = document.createElement('i');
      icon.setAttribute('data-feather', 'shopping-cart');
      icon.classList.add('feather-sm');

      // Add icon before text
      button.prepend(icon);

      // Add a space after the icon
      button.insertBefore(document.createTextNode(' '), icon.nextSibling);
    });

    // Re-run Feather icons replacement
    if (typeof feather !== 'undefined') {
      feather.replace();
    }
  }

  // Run initially
  addIconsToButtons();

  // Also run after AJAX requests complete (for dynamically added buttons)
  jQuery(document).ajaxComplete(function() {
    setTimeout(addIconsToButtons, 100);
  });

  // For WooCommerce AJAX add to cart
  jQuery(document.body).on('added_to_cart', function() {
    setTimeout(addIconsToButtons, 100);
  });
});
</script>
<?php
}


function add_rfq_button_to_product() {
	global $product;
	echo '<a href="#" class="button rfq-button icon-with-text"><i data-feather="file-text" class="feather-sm"></i> Add to RFQ</a>';
}
add_action('woocommerce_after_add_to_cart_button', 'add_rfq_button_to_product');

function add_buy_now_button_to_product() {
	global $product;
	echo '<a href="#" class="button rfq-button icon-with-text"><i data-feather="credit-card" class="feather-sm"></i> Buy Now</a>';
}
add_action('woocommerce_after_add_to_cart_button', 'add_buy_now_button_to_product');



// rating saction
function custom_product_rating_section() {
	global $product;

	// Get product rating
	$average = $product->get_average_rating();
	$rating_count = $product->get_rating_count();
	$review_count = $product->get_review_count();

	// Get total sales (from WooCommerce metadata)
	$total_sales = get_post_meta($product->get_id(), 'total_sales', true);

	// Output the section
	echo '<div class="custom-product-rating mt-2 mb-4 pb-4">';

	// Display star rating
	if ($rating_count > 0) {
			echo wc_get_rating_html($average, $rating_count);
			echo '<span class="rating-average"> ' . $average . ' |  </span>';
			echo '<span class="rating-count"> ' . $review_count . ' Reviews </span>';
	} else {
			echo '<span>No reviews yet</span>';
	}


	// Display sales count
	if ($total_sales) {
			echo '<span class="sales-count"> | ' . esc_html($total_sales) . ' Sold</span>';
	}else{
		echo '<span class="sales-count"> | 0 Sold</span>';
	}

	echo '</div>';
}

// Hook it into the product summary section
add_action('woocommerce_single_product_summary', 'custom_product_rating_section', 8);

// add to favorite / compare button

function add_wishlist_and_compare_buttons() {
	global $product;

	// Start the wrapper div
	echo '<div class="custom-buttons-wrapper mt-4 mb-4">';

	// Ensure YITH Wishlist is active
	if (function_exists('YITH_WCWL')) {
		echo do_shortcode('[yith_wcwl_add_to_wishlist]');
	}


	// Add compare button (works with or without YITH Compare)
	$product_id = $product->get_id();

	echo '<div class="custom-buttons-compare">';
	echo '<a href="#" class="yith-compare-btn compare-btn compare-button icon-with-text" data-product-id="' . esc_attr($product_id) . '" title="Compare">';
	echo '<i data-feather="repeat" class="feather-sm"></i>';
	echo '<span>Compare</span>';
	echo '</a>';
	echo '</div>';

	// Add share button with functionality
	global $product;
	$product_url = get_permalink($product->get_id());
	$product_title = get_the_title($product->get_id());
	$product_image = wp_get_attachment_image_url($product->get_image_id(), 'large');

	echo '<div class="share-button icon-with-text" data-product-url="' . esc_url($product_url) . '" data-product-title="' . esc_attr($product_title) . '" data-product-image="' . esc_url($product_image) . '">';
	echo '<i data-feather="share-2" class="feather-sm"></i><span>Share</span>';
	echo '</div>';

	// Add share modal/dropdown (hidden by default)
	echo '<div class="share-modal" id="share-modal" style="display: none;">';
	echo '<div class="share-modal-content">';
	echo '<div class="share-modal-header">';
	echo '<h4>Share this product</h4>';
	echo '<button class="share-modal-close" type="button">&times;</button>';
	echo '</div>';
	echo '<div class="share-options">';

	// Social sharing options
	echo '<a href="#" class="share-option facebook" data-platform="facebook">';
	echo '<i data-feather="facebook" class="feather-sm"></i>';
	echo '<span>Facebook</span>';
	echo '</a>';

	echo '<a href="#" class="share-option twitter" data-platform="twitter">';
	echo '<i data-feather="twitter" class="feather-sm"></i>';
	echo '<span>Twitter</span>';
	echo '</a>';

	echo '<a href="#" class="share-option whatsapp" data-platform="whatsapp">';
	echo '<i data-feather="message-circle" class="feather-sm"></i>';
	echo '<span>WhatsApp</span>';
	echo '</a>';

	echo '<a href="#" class="share-option email" data-platform="email">';
	echo '<i data-feather="mail" class="feather-sm"></i>';
	echo '<span>Email</span>';
	echo '</a>';

	echo '<a href="#" class="share-option copy-link" data-platform="copy">';
	echo '<i data-feather="link" class="feather-sm"></i>';
	echo '<span>Copy Link</span>';
	echo '</a>';

	echo '</div>';
	echo '</div>';
	echo '</div>';

	// Close wrapper div
	echo '</div>';
}
// add_action('woocommerce_single_product_summary', 'add_wishlist_and_compare_buttons', 6);


// add store link

// function add_store_link_on_product_page() {
// 	// Change this URL to your store page
// 	$store_url = home_url('/store/');

// 	echo '<div class="store-link">';
// 	echo '<a href="' . esc_url($store_url) . '" class="btn-store-link">';
// 	echo '<i class="fas fa-store"></i> Visit Our Store';
// 	echo '</a>';
// 	echo '</div>';
// }
// add_action('woocommerce_single_product_summary', 'add_store_link_on_product_page', 7);



function add_vendor_store_link() {
	global $product;

	if (function_exists('wcfmmp_get_store_url')) {
			$vendor_id = get_post_field('post_author', $product->get_id());
			$store_url = wcfmmp_get_store_url($vendor_id);

			echo '<div class="vendor-store-link mt-4 mb-4">';
			echo '<a href="' . esc_url($store_url) . '" class="btn-store-link icon-with-text">';
			echo '<i data-feather="shopping-bag" class="feather-sm"></i> Visit Vendor Store';
			echo '</a>';
			echo '</div>';
	}
}
add_action('woocommerce_single_product_summary', 'add_vendor_store_link', 7);








// sale price arrange
function custom_rearrange_product_price($price, $product) {
	if ($product->is_on_sale()) {
			$regular_price = wc_get_price_to_display($product, ['price' => $product->get_regular_price()]);
			$sale_price = wc_get_price_to_display($product, ['price' => $product->get_sale_price()]);

			// Display Sale Price First, then Regular Price (crossed out)
			return '<span class="sale-price">' . wc_price($sale_price) . '</span> <del class="regular-price">' . wc_price($regular_price) . '</del>';
	}
	return $price;
}
add_filter('woocommerce_get_price_html', 'custom_rearrange_product_price', 10, 2);


//attribut and variable product

function add_attribut_variable_product() {
	global $product;

	if (!$product) {
		return; // Exit if no product is found
	}

	// Get product ID
	$product_id = $product->get_id();

	// Check if this is a variable product
	$is_variable = $product->is_type('variable');
	$variation_attributes = $is_variable ? $product->get_variation_attributes() : array();

	// Get all product attributes
	$attributes = $product->get_attributes();

	// Display each attribute as a configuration option
	if (!empty($attributes)) {
			foreach ($attributes as $attribute_name => $attribute) {
					// Skip attributes that are not visible on the product page
					if (!$attribute->get_visible()) {
							continue;
					}

					// Get attribute label
					$attribute_label = wc_attribute_label($attribute_name);

					// Check if this is a variation attribute
					$is_variation_attribute = $is_variable && isset($variation_attributes[$attribute_name]);

					// Get attribute values
					$values = array();

					if ($attribute->is_taxonomy()) {
							// For taxonomy-based attributes
							$terms = wp_get_post_terms($product_id, $attribute->get_name(), array('fields' => 'all'));

							foreach ($terms as $term) {
									$values[] = array(
											'name' => $term->name,
											'slug' => $term->slug,
									);
							}
					} else {
							// For custom product attributes
							$values_array = $attribute->get_options();
							foreach ($values_array as $value) {
									$values[] = array(
											'name' => $value,
											'slug' => sanitize_title($value),
									);
							}
					}

					// Skip if no values
					if (empty($values)) {
							continue;
					}

					// Start configuration section
					echo '<div class="product-config' . ($is_variation_attribute ? ' variation-attribute' : '') . '">';
					echo '<h4 class="config-title">' . esc_html($attribute_label) . ':</h4>';
					echo '<div class="config-options" data-attribute="' . esc_attr(sanitize_title($attribute_name)) . '">';

					// For variation attributes, get the available options
					$available_options = array();
					if ($is_variation_attribute) {
							$available_options = $variation_attributes[$attribute_name];
					}

					// Display each value as a selectable option
					$first = true;
					foreach ($values as $value) {
							// For variation attributes, check if this value is available
							$is_available = !$is_variation_attribute || in_array($value['slug'], $available_options);

							// Only select the first available option
							$selected = $first && $is_available ? ' selected' : '';
							if ($selected) {
									$first = false;
							}

							// Add disabled class if not available
							$disabled = !$is_available ? ' disabled' : '';

							echo '<div class="config-option' . $selected . $disabled . '" ' .
									 'data-group="' . esc_attr(sanitize_title($attribute_name)) . '" ' .
									 'data-value="' . esc_attr($value['slug']) . '" ' .
									 'data-variation="' . ($is_variation_attribute ? '1' : '0') . '">' .
									 esc_html($value['name']) . '</div>';
					}

					echo '</div>';
					echo '</div>';
			}
	}

	// Add hidden form for variable products
	if ($product && $product->is_type('variable')) {
			echo '<div class="hidden-form-container">';
			echo '<form class="cart hidden-form" method="post" enctype="multipart/form-data">';
			echo '<input type="hidden" name="add-to-cart" value="' . esc_attr($product_id) . '">';
			echo '<input type="hidden" name="quantity" class="hidden-qty" value="1">';

			// Add variation ID field
			echo '<input type="hidden" name="variation_id" class="variation_id" value="">';

			// Add hidden fields for each variation attribute
			$variation_attributes = $product->get_variation_attributes();
			foreach ($variation_attributes as $attribute_name => $options) {
					$attribute_name_clean = sanitize_title($attribute_name);

					// Get the default value (first option)
					$default_value = '';
					if (!empty($options)) {
							if (is_array($options)) {
									$default_value = reset($options);
							} else {
									$default_value = $options;
							}
					}

					echo '<input type="hidden" name="attribute_' . esc_attr($attribute_name_clean) . '" value="' . esc_attr($default_value) . '">';
			}

			// Add a hidden div with variation data for JavaScript
			echo '<div id="product-variations-data" style="display:none;" data-product_variations=\'' .
					 esc_attr(wp_json_encode($product->get_available_variations())) .
					 '\'></div>';

			// Add nonce field for security
			wp_nonce_field('woocommerce-add-to-cart', 'woocommerce-add-to-cart-nonce');

			echo '</form>';
			echo '</div>';
	}









}
add_action('woocommerce_single_product_summary', 'add_attribut_variable_product', 11);



function custom_review_script() {
    ?>
<script>
jQuery(document).ready(function($) {
  $("#write-review-btn").click(function() {
    $("#review_form").slideToggle(400); // Toggle the review form

    // Change button text dynamically
    var buttonText = $(this).text() === "Add Your Review" ? "Hide Review Form" : "Add Your Review";
    $(this).text(buttonText);

    // Scroll to the review form when opening
    if ($("#review_form").is(":visible")) {
      $("html, body").animate({
        scrollTop: $("#review_form").offset().top - 100
      }, 800);
    }
  });
});
</script>
<?php
}
add_action('wp_footer', 'custom_review_script');

// coupon
function apply_coupon_ajax() {
	if (isset($_POST['coupon_code'])) {
			$coupon_code = sanitize_text_field($_POST['coupon_code']);
			WC()->cart->apply_coupon($coupon_code);

			if (WC()->cart->has_discount($coupon_code)) {
					wp_send_json_success();
			} else {
					wp_send_json_error(['message' => 'Invalid or expired coupon.']);
			}
	}
	wp_die();
}
add_action('wp_ajax_apply_coupon', 'apply_coupon_ajax');
add_action('wp_ajax_nopriv_apply_coupon', 'apply_coupon_ajax');

/**
 * Handle removing selected cart items
 */
function remove_selected_cart_items() {
    // Check if cart item keys are provided
    if (isset($_POST['cart_item_keys']) && is_array($_POST['cart_item_keys'])) {
        $cart_item_keys = array_map('sanitize_text_field', $_POST['cart_item_keys']);

        // Remove each item from the cart
        foreach ($cart_item_keys as $cart_item_key) {
            WC()->cart->remove_cart_item($cart_item_key);
        }

        // Send success response
        wp_send_json_success(array('message' => 'Items removed successfully'));
    } else {
        // Send error response
        wp_send_json_error(array('message' => 'No items selected'));
    }

    wp_die();
}
add_action('wp_ajax_remove_selected_cart_items', 'remove_selected_cart_items');
add_action('wp_ajax_nopriv_remove_selected_cart_items', 'remove_selected_cart_items');



/**
 * Handle contact form submissions
 */
function submit_contact_form() {
    // Verify nonce if you added one
    // if (!check_ajax_referer('contact_form_nonce', 'security', false)) {
    //     wp_send_json_error('Security check failed');
    //     wp_die();
    // }

    // Get form data
    $name = isset($_POST['full_name']) ? sanitize_text_field($_POST['full_name']) : '';
    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
    $subject = isset($_POST['subject']) ? sanitize_text_field($_POST['subject']) : '';
    $message = isset($_POST['message']) ? sanitize_textarea_field($_POST['message']) : '';

    // Validate data
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        wp_send_json_error('Please fill in all required fields');
        wp_die();
    }

    if (!is_email($email)) {
        wp_send_json_error('Please enter a valid email address');
        wp_die();
    }

    // Prepare email content
    $to = get_option('admin_email');
    $email_subject = 'Contact Form: ' . $subject;
    $email_message = "Name: $name\n";
    $email_message .= "Email: $email\n\n";
    $email_message .= "Message:\n$message";
    $headers = "From: $name <$email>" . "\r\n";

    // Send email
    $mail_sent = wp_mail($to, $email_subject, $email_message, $headers);

    if ($mail_sent) {
        wp_send_json_success('Your message has been sent successfully');
    } else {
        wp_send_json_error('There was an error sending your message');
    }

    wp_die();
}
add_action('wp_ajax_submit_contact_form', 'submit_contact_form');
add_action('wp_ajax_nopriv_submit_contact_form', 'submit_contact_form');

/**
 * AJAX add to cart functionality
 *
 * Note: This functionality is now handled by the tendeal_ajax_add_to_cart function
 * defined earlier in this file.
 */

/**
 * Override WooCommerce account templates with custom ones
 */
function tendeal_override_account_templates($template, $template_name, $template_path) {
    // Define which templates to override
    $custom_templates = array(
			 // Account templates
        'myaccount/form-login.php' => 'woocommerce/myaccount/custom-login.php',
        'myaccount/form-lost-password.php' => 'woocommerce/myaccount/custom-lost-password.php',


        // Checkout templates
        'checkout/form-checkout.php' => 'woocommerce/checkout/form-checkout-custom-new.php',
        'checkout/thankyou.php' => 'woocommerce/checkout/thankyou-custom.php',
        'checkout/order-received.php' => 'woocommerce/checkout/order-received-custom.php'
     );

    // Check if the template is one we want to override
    if (isset($custom_templates[$template_name])) {
        $custom_template = get_stylesheet_directory() . '/' . $custom_templates[$template_name];

        // If the custom template exists, use it
        if (file_exists($custom_template)) {
            return $custom_template;
        }
    }

    // Return the original template if no custom one is found
    return $template;
}
add_filter('woocommerce_locate_template', 'tendeal_override_account_templates', 10, 3);

/**
 * Add custom registration form
 */
function tendeal_custom_registration_form() {
    // Check if we're on the account page with the register action
    if (is_account_page() && isset($_GET['action']) && $_GET['action'] === 'register') {
        // Enqueue the account pages CSS directly to ensure it's loaded
        wp_enqueue_style('tendeal-account-pages', get_stylesheet_directory_uri() . '/css/account-pages.css', array(), _S_VERSION);

        // Load our custom template
        load_template(get_stylesheet_directory() . '/woocommerce/myaccount/custom-register.php', false);
        exit;
    }
}
add_action('template_redirect', 'tendeal_custom_registration_form', 20);

/**
 * Handle YITH WooCommerce Wishlist AJAX removal
 */
function tendeal_handle_wishlist_removal() {
    // Check if YITH WooCommerce Wishlist is active
    if (!function_exists('YITH_WCWL')) {
        return;
    }

    // Add filter to redirect after wishlist removal
    add_filter('yith_wcwl_redirect_after_remove', function($redirect) {
        // Return false to prevent default redirect
        return false;
    });

    // Add filter to handle AJAX response
    add_filter('yith_wcwl_ajax_remove_response', function($response) {
        // Add success flag to response
        $response['result'] = true;
        return $response;
    });
}
add_action('init', 'tendeal_handle_wishlist_removal');

// AJAX handler for cart count
function get_cart_count_ajax() {
    $count = WC()->cart->get_cart_contents_count();
    wp_send_json_success(array('count' => $count));
}
add_action('wp_ajax_get_cart_count', 'get_cart_count_ajax');
add_action('wp_ajax_nopriv_get_cart_count', 'get_cart_count_ajax');

// AJAX handler for cart dropdown content
function get_cart_dropdown_content_ajax() {
    ob_start();

    if (WC()->cart && !WC()->cart->is_empty()) {
        echo '<div class="mini-cart-header">';
        echo '<h4>Shopping Cart (' . WC()->cart->get_cart_contents_count() . ')</h4>';
        echo '</div>';
        echo '<div class="mini-cart-items">';
        woocommerce_mini_cart();
        echo '</div>';
    } else {
        echo '<div class="empty-cart-message">';
        echo '<i data-feather="shopping-cart" class="feather-lg"></i>';
        echo '<p>Your cart is empty</p>';
        echo '<a href="' . wc_get_page_permalink('shop') . '" class="btn btn-primary">Start Shopping</a>';
        echo '</div>';
    }

    $content = ob_get_clean();
    $count = WC()->cart ? WC()->cart->get_cart_contents_count() : 0;

    wp_send_json_success(array(
        'content' => $content,
        'count' => $count
    ));
}
add_action('wp_ajax_get_cart_dropdown_content', 'get_cart_dropdown_content_ajax');
add_action('wp_ajax_nopriv_get_cart_dropdown_content', 'get_cart_dropdown_content_ajax');



// ge store product

function get_store_products() {
	if (isset($_POST['seller_id'])) {
			$seller_id = intval($_POST['seller_id']);

			$args = array(
					'post_type' => 'product',
					'posts_per_page' => -1,
					// 'tax_query' => array(
					// 		array(
					// 				'taxonomy' => 'store',  // Replace with the actual taxonomy name
					// 				'field'    => 'term_id',
					// 				'terms'    => $seller_id,
					// 		),
					// ),
			);

			$products = new WP_Query($args);

			$response = array();

			if ($products->have_posts()) {
					while ($products->have_posts()) {
							$products->the_post();
							global $product;

							$response[] = array(
									'id'    => get_the_ID(),  // Add Product ID
									'title' => get_the_title(),
									'link'  => get_permalink(),
									'image' => get_the_post_thumbnail_url(get_the_ID(), 'medium'),
									'price' => $product->get_price_html(),
							);
					}
			}

			wp_send_json_success(array(
				'message' => __('Product fetch successfully'),
				'products' => $response
		));

			wp_reset_postdata();

			// echo json_encode($response); // Send response as JSON
	}
	wp_die();
}
add_action('wp_ajax_get_store_products', 'get_store_products');
add_action('wp_ajax_nopriv_get_store_products', 'get_store_products');


/////////////////////////////


// Function to get WooCommerce categories and subcategories
function get_woocommerce_categories_with_subcategories() {
    $categories = get_terms( array(
        'taxonomy'   => 'product_cat',
        'parent'     => 0,
        'hide_empty' => false, // Ensure categories with no products are also retrieved
    ) );

    $category_list = array();

    foreach ( $categories as $category ) {

 			// Get category thumbnail if available
 			$thumbnail_id = get_term_meta($category->term_id, 'thumbnail_id', true);
 			$has_thumbnail = false;

			 if ($thumbnail_id) {
				$thumbnail = wp_get_attachment_url($thumbnail_id);
				if ($thumbnail) {
					$has_thumbnail = true;
				}
			}else{
				$thumbnail= get_template_directory_uri() . '/img/category-icons/default-icon.svg';
			}

        $category_list[] = array(
            'id'       => $category->term_id,
            'name'     => $category->name,
            'slug'     => $category->slug,
						'thumbnail' => $thumbnail,
            'url'      => get_term_link( $category ),
            'children' => get_subcategory_list( $category->term_id ),
        );
    }

    return $category_list;
}

// Function to get subcategories of a category
function get_subcategory_list( $parent_id ) {
    $subcategories = get_terms( array(
        'taxonomy'   => 'product_cat',
        'parent'     => $parent_id,
        'hide_empty' => false,  //show empty subcategories
    ) );

    $subcategory_list = array();
    foreach ( $subcategories as $subcategory ) {
        $subcategory_list[] = array(
            'id'   => $subcategory->term_id,
            'name' => $subcategory->name,
            'slug' => $subcategory->slug,
             'url'  => get_term_link( $subcategory ),
        );
    }
    return $subcategory_list;
}

// Function to generate the HTML for the category button and dropdown
function woocommerce_category_button_html() {
	$categories = get_woocommerce_categories_with_subcategories();

	if ( empty( $categories ) ) {
	return '<p>No product categories found.</p>'; //check if there are categories
	}

	$html = '<div class="category-menu-wrapper">';
		$html .= '<button type="button" class="category-menu-button" id="category-menu-toggle">';
			$html .= ' <span>Categories</span> <i data-feather="menu" class="feather-sm"></i>';
			$html .= '</button>';

		// Categories dropdown
		$html .= '<div class="category-dropdown" id="category-menu-dropdown">';
			$html .= '<div class="category-dropdown-content">';

				// Left column - Main categories
				$html .= '<div class="category-column category-main-list">';
					$html .= '<ul class="category-list">';

						// Add data attributes to each category for hover functionality
						foreach ($categories as $index => $category) {
						$active_class = ($index === 0) ? ' active' : '';
						$html .= '<li class="category-item' . $active_class . '" data-category-id="' . esc_attr($category['id']) . '">
							';
							$html .= '<a href="' . esc_url($category['url']) . '">';
								$html .= '<img src="' . esc_url($category['thumbnail']) . '" alt="' . esc_attr($category['name']) . '" class="category-icon">';
								$html .= '<span>' . esc_html($category['name']) . '</span>';
								$html .= '<i data-feather="chevron-right" class="feather-sm"></i>';
								$html .= '</a>';
							$html .= '</li>';
						}

						$html .= '</ul>';
					$html .= '</div>'; // End left column

				// Right column - Subcategories (will be populated by JavaScript on hover)
				$html .= '<div class="category-column category-subcategory-container">';

					// Create a container for each category's subcategories
					foreach ($categories as $index => $category) {
					$display_style = ($index === 0) ? ' style="display: block;"' : ' style="display: none;"';
					$html .= '<div class="subcategory-content"
						id="subcategory-' . esc_attr($category['id']) . '"' . $display_style . '>';

						// Add category title
						$html .= '<h3 class="subcategory-title">' . esc_html($category['name']) . '</h3>';

						// Group subcategories into sections (up to 3 columns)
						if (!empty($category['children'])) {
						// Determine how many subcategories to show per column
						$total_subcategories = count($category['children']);
						$subcategories_per_column = ceil($total_subcategories / 3); // Aim for 3 columns
						$subcategory_chunks = array_chunk($category['children'], $subcategories_per_column);

						$html .= '<div class="subcategory-columns">';

							foreach ($subcategory_chunks as $subcategory_chunk) {
							$html .= '<div class="subcategory-column">';

								foreach ($subcategory_chunk as $subcategory) {
								$html .= '<a href="' . esc_url($subcategory['url']) . '" class="category-link">';
									$html .= esc_html($subcategory['name']);
									$html .= '</a>';
								}

								$html .= '</div>'; // End subcategory-column
							}

							$html .= '</div>'; // End subcategory-columns

						// Add "View all" link
						$html .= '<a href="' . esc_url($category['url']) . '" class="category-link view-all">';
							$html .= 'View all ' . esc_html($category['name']);
							$html .= '</a>';
						} else {
						$html .= '<p class="no-subcategories">No subcategories found.</p>';
						}

						$html .= '</div>'; // End subcategory-content
					}

					$html .= '</div>'; // End right column

				$html .= '</div>'; // End category-dropdown-content
			$html .= '</div>'; // End category-dropdown
		$html .= '</div>'; // End category-menu-wrapper

	return $html;
	}

//shortcode to display the category dropdown
function category_button_shortcode(){
     return woocommerce_category_button_html();
}
add_shortcode('category_button', 'category_button_shortcode');

// Add the function to a hook (e.g., to display in a specific location in your theme)
//add_action( 'woocommerce_before_shop_loop', 'woocommerce_category_button_html', 10 ); //priority 10
//add_action( 'your_theme_hook', 'woocommerce_category_button_html', 10 );  //use a theme hook

/**
 * Additional fix for first item being empty in product loops
 * This runs on every WooCommerce loop item to ensure product is properly set
 */
function tendeal_fix_first_item_empty() {
    global $product, $post;

    // Only run on WooCommerce pages and product loops
    if (!function_exists('is_woocommerce')) {
        return;
    }

    // Ensure we have a valid product object
    if (empty($product) || !is_a($product, 'WC_Product')) {
        if (!empty($post) && isset($post->ID) && get_post_type($post->ID) === 'product') {
            $product = wc_get_product($post->ID);
            if ($product && is_a($product, 'WC_Product')) {
                $GLOBALS['product'] = $product;
            }
        }
    }
}
add_action('woocommerce_before_shop_loop_item', 'tendeal_fix_first_item_empty', 1);

/**
 * Fix for shortcodes and front page product displays
 */
function tendeal_fix_shortcode_product_loop() {
    global $product, $post;

    // Ensure we have a valid product object for shortcodes
    if (empty($product) || !is_a($product, 'WC_Product')) {
        if (!empty($post) && isset($post->ID) && get_post_type($post->ID) === 'product') {
            $product = wc_get_product($post->ID);
            if ($product && is_a($product, 'WC_Product')) {
                $GLOBALS['product'] = $product;
            }
        }
    }
}
add_action('woocommerce_shortcode_before_products_loop', 'tendeal_fix_shortcode_product_loop', 1);

/**
 * Force product object setup in loops - last resort fix
 */
function tendeal_force_product_setup() {
    global $product, $post;

    // Only run during the loop
    if (!in_the_loop()) {
        return;
    }

    // Only for product post types
    if (!$post || get_post_type($post) !== 'product') {
        return;
    }

    // Force product object creation if missing
    if (empty($product) || !is_a($product, 'WC_Product')) {
        $product = wc_get_product($post->ID);
        $GLOBALS['product'] = $product;
    }
}
add_action('the_post', 'tendeal_force_product_setup', 1);

/**
 * Include cart address handler for AJAX functionality
 */
require_once get_template_directory() . '/inc/cart-address-handler.php';

/**
 * Get selected vendor shipping methods from session/user meta
 *
 * @return array Array of selected shipping methods by vendor
 */
function tendeal_get_selected_vendor_shipping_methods() {
    $selected_methods = array();

    // First try to get from WooCommerce session
    if ( WC()->session ) {
        $selected_methods = WC()->session->get('vendor_shipping_methods', array());
    }

    // If logged in, also check user meta (fallback or override)
    if ( is_user_logged_in() && empty( $selected_methods ) ) {
        $user_methods = get_user_meta( get_current_user_id(), 'vendor_shipping_methods', true );
        if ( is_array( $user_methods ) ) {
            $selected_methods = $user_methods;
        }
    }

    return $selected_methods;
}

/**
 * Get shipping cost for specific vendor based on selected method
 *
 * @param string $vendor_id Vendor ID
 * @return float Shipping cost
 */
function tendeal_get_vendor_shipping_cost( $vendor_id ) {
    $selected_methods = tendeal_get_selected_vendor_shipping_methods();
    $selected_method = isset( $selected_methods[$vendor_id] ) ? $selected_methods[$vendor_id] : '';

    if ( empty( $selected_method ) ) {
        return 15.00; // Default shipping cost
    }

    // Parse method ID to get cost
    $parts = explode( '_', $selected_method );
    $method_type = $parts[0];

    switch ( $method_type ) {
        case 'express':
            return 25.00;
        case 'overnight':
            return 35.00;
        case 'standard':
        default:
            return 15.00;
    }
}

/**
 * Get available shipping methods for vendor cart items
 *
 * @param array $vendor_cart_items Cart items for specific vendor
 * @return array Array of shipping methods with costs
 */
function tendeal_get_vendor_shipping_methods( $vendor_cart_items ) {
    $shipping_methods = array();

    if ( empty( $vendor_cart_items ) ) {
        return $shipping_methods;
    }

    // Create a temporary package for this vendor's items
    $vendor_package = array(
        'contents' => array(),
        'contents_cost' => 0,
        'applied_coupons' => WC()->cart->get_applied_coupons(),
        'user' => array(
            'ID' => get_current_user_id()
        ),
        'destination' => array(
            'country' => WC()->customer->get_shipping_country(),
            'state' => WC()->customer->get_shipping_state(),
            'postcode' => WC()->customer->get_shipping_postcode(),
            'city' => WC()->customer->get_shipping_city(),
            'address' => WC()->customer->get_shipping_address(),
            'address_2' => WC()->customer->get_shipping_address_2()
        )
    );

    // Add vendor items to package
    foreach ( $vendor_cart_items as $cart_item_key => $cart_item ) {
        $vendor_package['contents'][$cart_item_key] = $cart_item;
        $vendor_package['contents_cost'] += $cart_item['line_total'];
    }

    // Get shipping zones and methods
    $shipping_zones = WC_Shipping_Zones::get_zones();
    $shipping_zones[] = WC_Shipping_Zones::get_zone_by( 'zone_id', 0 ); // Add default zone

    foreach ( $shipping_zones as $zone ) {
        if ( is_array( $zone ) && isset( $zone['shipping_methods'] ) ) {
            $zone_methods = $zone['shipping_methods'];
        } elseif ( is_object( $zone ) ) {
            $zone_methods = $zone->get_shipping_methods( true );
        } else {
            continue;
        }

        foreach ( $zone_methods as $method ) {
            if ( $method->is_enabled() ) {
                // Calculate shipping cost for this method
                $rates = $method->get_rates_for_package( $vendor_package );

                foreach ( $rates as $rate ) {
                    $method_id = $rate->get_id();
                    $method_label = $rate->get_label();
                    $method_cost = $rate->get_cost();

                    // Add meta data if available
                    $meta_data = $rate->get_meta_data();
                    $delivery_time = '';
                    $description = '';

                    if ( isset( $meta_data['delivery_time'] ) ) {
                        $delivery_time = $meta_data['delivery_time'];
                    } elseif ( strpos( strtolower( $method_label ), 'express' ) !== false ) {
                        $delivery_time = __('3-7 days', 'tendeal');
                    } elseif ( strpos( strtolower( $method_label ), 'standard' ) !== false ) {
                        $delivery_time = __('7-15 days', 'tendeal');
                    } else {
                        $delivery_time = __('5-10 days', 'tendeal');
                    }

                    if ( isset( $meta_data['description'] ) ) {
                        $description = $meta_data['description'];
                    } else {
                        $description = $method_label;
                    }

                    $shipping_methods[$method_id] = array(
                        'name' => $method_label,
                        'cost' => floatval( $method_cost ),
                        'time' => $delivery_time,
                        'description' => $description
                    );
                }
            }
        }
    }

    // Fallback to default methods if no WooCommerce methods found
    if ( empty( $shipping_methods ) ) {
        $shipping_methods = array(
            'flat_rate' => array(
                'name' => __('Standard Shipping', 'tendeal'),
                'cost' => 15.00,
                'time' => __('7-15 days', 'tendeal'),
                'description' => __('Regular delivery service', 'tendeal')
            ),
            'express_shipping' => array(
                'name' => __('Express Shipping', 'tendeal'),
                'cost' => 25.00,
                'time' => __('3-7 days', 'tendeal'),
                'description' => __('Faster delivery service', 'tendeal')
            )
        );
    }

    return $shipping_methods;
}